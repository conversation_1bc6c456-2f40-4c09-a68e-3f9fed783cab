import {
  createSvgIcon
} from "./chunk-6DRQ3NX2.js";
import {
  require_jsx_runtime
} from "./chunk-NJLIVH7H.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/icons-material/esm/PlayArrow.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var PlayArrow_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M8 5v14l11-7z"
}), "PlayArrow");

export {
  PlayArrow_default
};
//# sourceMappingURL=chunk-G6LHQVHU.js.map
