import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  IconButton,

  Card,
  CardMedia,
  CardContent,
  CardActions,
  Chip,
  Paper,
  Stack,
  Rating,
  Badge,
  Tooltip,
  Fade,
  Divider,
  Avatar
} from '@mui/material';
import GridViewIcon from '@mui/icons-material/GridView';
import ListIcon from '@mui/icons-material/List';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import StarIcon from '@mui/icons-material/Star';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import InfoIcon from '@mui/icons-material/Info';
import { masterProductList } from'../../data/masterProductList';
import { useNavigate } from 'react-router-dom';

function ProductListView({ setViewMode, filters, isSmallScreen, setSelectedProduct }) {
  const navigate = useNavigate();
  const [wishlist, setWishlist] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState(masterProductList);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      let result = [...masterProductList];

      // Apply all filters only if at least one filter is set
      const hasAnyFilter = Object.values(filters).some(
        (v) => Array.isArray(v) ? v.length > 0 : v && v !== '' && v !== false && v !== 0 && v !== 'all'
      );

      if (hasAnyFilter) {
        if (filters.searchVin) {
          result = result.filter((product) =>
            product.vinCompatible && product.vinCompatible.includes(filters.searchVin)
          );
        }
        if (filters.makes.length > 0) {
          result = result.filter((product) =>
            product.vehicleCompatibility.some((v) => filters.makes.includes(v.make))
          );
        }
        if (filters.models.length > 0) {
          result = result.filter((product) =>
            product.vehicleCompatibility.some((v) => filters.models.includes(v.model))
          );
        }
        if (filters.years.length > 0) {
          result = result.filter((product) =>
            product.vehicleCompatibility.some((v) => filters.years.includes(v.year))
          );
        }
        if (filters.categories.length > 0) {
          result = result.filter((product) => filters.categories.includes(product.category.en));
        }
        if (filters.brands.length > 0) {
          result = result.filter((product) => filters.brands.includes(product.brand.en));
        }
        if (filters.materials.length > 0) {
          result = result.filter((product) => filters.materials.includes(product.material.en));
        }
        if (filters.warranties.length > 0) {
          result = result.filter((product) => filters.warranties.includes(product.warranty.en));
        }
        if (filters.stockLocations.length > 0) {
          result = result.filter((product) =>
            product.stock.locations && filters.stockLocations.some((loc) => product.stock.locations.includes(loc))
          );
        }
        if (filters.priceRange && Array.isArray(filters.priceRange)) {
          result = result.filter((product) =>
            product.priceUSD >= filters.priceRange[0] && product.priceUSD <= filters.priceRange[1]
          );
        }
        if (filters.partType === 'primary') {
          result = result.filter((product) => product.partType === 'primary');
        }
        if (!filters.includeSuperseded) {
          result = result.filter((product) => !product.tags.en.includes('Superseded'));
        }
      }

      // Add searchText filter from filters
      if (filters.searchText && filters.searchText.length > 0) {
        const input = filters.searchText.toLowerCase();
        result = result.filter(product =>
          product.name?.en?.toLowerCase().includes(input) ||
          product.brand?.en?.toLowerCase().includes(input) ||
          product.category?.en?.toLowerCase().includes(input) ||
          product.partNumber?.toLowerCase().includes(input)
        );
      }

      setFilteredProducts(result);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [filters]);

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  const toggleWishlist = (productId) => {
    setWishlist((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  return (
    <Box sx={{ width: '100%', py: { xs: 1, sm: 2 }, px: { xs: 0, sm: 1 } }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: { xs: 1, sm: 2 },
          gap: { xs: 1, sm: 1.5 },
          px: { xs: 0, sm: 0 },
        }}
      >
        <Typography
          variant="h6"
          color="text.primary"
          sx={{ fontSize: { xs: '1rem', sm: '1.25rem' }, fontWeight: 600 }}
        >
          All Products ({filteredProducts.length})
        </Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: 1,
            alignItems: { xs: 'stretch', sm: 'center' },
            width: { xs: '100%', sm: 'auto' },
          }}
        >
        
        </Box>
     
      </Box>

      <Box
        sx={{
          width: '100%',
          maxWidth: '100%',
          p: { xs: 1, sm: 1.5, md: 2, lg: 2.5 }, // Progressive responsive padding
          display: 'flex',
          flexDirection: 'column',
          gap: { xs: 1, sm: 1.5, md: 2, lg: 2.5 }, // Progressive responsive gap
          alignItems: 'stretch',
        }}
      >
        {filteredProducts.map((product) => (
          <Box
            key={product.id}
            sx={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            <Card
              className="product-card-list"
              onClick={() => navigate(`/product/${product.id}`)}
              sx={{
                display: 'flex',
                flexDirection: 'row',
                width: '100%',
                maxWidth: '100%',
                minWidth: 0,
                minHeight: { xs: 160, sm: 180, md: 200, lg: 220 }, // Increased height for better content visibility
                borderRadius: { xs: 1, sm: 2 },
                overflow: 'hidden',
                background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                border: '1px solid rgba(46, 192, 203, 0.08)',
                boxShadow: '0 1px 6px rgba(46, 192, 203, 0.08)',
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                cursor: 'pointer',
                position: 'relative',
                flex: '1 1 auto', // Ensures consistent flex behavior
                '&:hover': {
                  transform: { xs: 'translateY(-1px)', sm: 'translateY(-2px)' },
                  boxShadow: '0 4px 16px rgba(46, 192, 203, 0.12)',
                  border: '1px solid rgba(46, 192, 203, 0.15)',
                  '& .list-image': {
                    transform: 'scale(1.02)',
                  },
                  '& .list-actions': {
                    opacity: 1,
                    transform: 'translateX(0)',
                  }
                },
              }}
            >
              {/* Enhanced Image Section */}
              <Box
                className="card-image"
                sx={{
                  position: 'relative',
                  background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                  height: '100%',
                  width: { xs: 120, sm: 140, md: 160, lg: 180 }, // Responsive image width
                  flexShrink: 0,
                  overflow: 'hidden',
                }}
              >
                <CardMedia
                  component="img"
                  image={product.imageUrl?.replace('./Assets/Images/', '/src/assets/images/') || '/src/assets/images/placeholder.jpg'}
                  alt={product.name.en}
                  className="list-image"
                  onError={(e) => {
                    e.target.src = '/src/assets/images/placeholder.jpg';
                  }}
                  sx={{
                    height: '100%',
                    width: '100%',
                    objectFit: 'cover',
                    transition: 'transform 0.3s ease',
                  }}
                />

                {/* Modern Tags */}
                <Stack
                  spacing={0.5}
                  sx={{
                    position: 'absolute',
                    top: 8,
                    left: 8,
                    zIndex: 3,
                  }}
                >
                  {product.tags.en.slice(0, 2).map((tag, index) => (
                    <Fade in timeout={300 + index * 100} key={tag}>
                      <Chip
                        icon={
                          tag === 'Best Seller' ? <StarIcon sx={{ fontSize: 12 }} /> :
                          tag === 'Trending' ? <TrendingUpIcon sx={{ fontSize: 12 }} /> :
                          tag === 'Special Offer' ? <LocalOfferIcon sx={{ fontSize: 12 }} /> :
                          undefined
                        }
                        label={tag}
                        size="small"
                        sx={{
                          height: 20,
                          borderRadius: 2,
                          color: 'white',
                          fontWeight: 600,
                          fontSize: '0.65rem',
                          background:
                            tag === 'Best Seller'
                              ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
                              : tag === 'Trending'
                              ? 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)'
                              : tag === 'Popular'
                              ? 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)'
                              : tag === 'Special Offer'
                              ? 'linear-gradient(135deg, #ffd43b 0%, #fab005 100%)'
                              : 'linear-gradient(135deg, #868e96 0%, #495057 100%)',
                          boxShadow: '0 2px 6px rgba(0, 0, 0, 0.15)',
                          '& .MuiChip-icon': {
                            color: 'white',
                            marginLeft: 0.5,
                          },
                        }}
                      />
                    </Fade>
                  ))}
                </Stack>

                {/* Enhanced Wishlist Button */}
                <Tooltip title={wishlist.includes(product.id) ? 'Remove from wishlist' : 'Add to wishlist'} arrow>
                  <IconButton
                    className="wishlist-icon"
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      zIndex: 4,
                      width: 32,
                      height: 32,
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                      backdropFilter: 'blur(10px)',
                      color: wishlist.includes(product.id) ? '#ff6b6b' : '#868e96',
                      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
                      '&:hover': {
                        transform: 'scale(1.1)',
                        bgcolor: 'white',
                        color: wishlist.includes(product.id) ? '#ff5252' : '#ff6b6b',
                      },
                      transition: 'all 0.2s ease',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleWishlist(product.id);
                    }}
                  >
                    {wishlist.includes(product.id) ?
                      <FavoriteIcon sx={{ fontSize: 16 }} /> :
                      <FavoriteBorderIcon sx={{ fontSize: 16 }} />
                    }
                  </IconButton>
                </Tooltip>
              </Box>
              {/* Enhanced Content Section */}
              <CardContent
                sx={{
                  flex: 1,
                  p: { xs: 1, sm: 1.5, md: 2 }, // Increased padding for better spacing
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  gap: { xs: 0.5, sm: 0.75, md: 1 }, // Responsive gaps
                  minWidth: 0, // Prevents content overflow
                  overflow: 'hidden', // Ensures content doesn't overflow
                }}
              >
                {/* Product Info */}
                <Box>
                  <Typography
                    variant="h6"
                    sx={{
                      color: 'text.primary',
                      fontWeight: 700,
                      fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem', lg: '1.2rem' },
                      lineHeight: 1.3,
                      mb: { xs: 0.5, sm: 0.75, md: 1 },
                      display: '-webkit-box',
                      WebkitLineClamp: { xs: 2, sm: 2, md: 2 }, // Allow 2 lines on all screens
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      wordBreak: 'break-word', // Better text wrapping
                    }}
                  >
                    {product.name.en}
                  </Typography>

                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={1}
                    sx={{
                      mb: { xs: 0.5, sm: 0.75, md: 1 },
                      flexWrap: 'wrap', // Allow wrapping on small screens
                      gap: 0.5 // Consistent gap for wrapped items
                    }}
                  >
                    <Chip
                      label={`#${product.partNumber}`}
                      size="small"
                      variant="outlined"
                      sx={{
                        height: 24,
                        fontSize: '0.75rem',
                        borderColor: 'primary.main',
                        color: 'primary.main',
                      }}
                    />
                    <Chip
                      label={product.stock.status}
                      size="small"
                      sx={{
                        height: 24,
                        fontSize: '0.75rem',
                        fontWeight: 600,
                        bgcolor:
                          product.stock.status === 'In Stock'
                            ? 'rgba(76, 175, 80, 0.1)'
                            : product.stock.status === 'Low Stock'
                            ? 'rgba(255, 193, 7, 0.1)'
                            : 'rgba(244, 67, 54, 0.1)',
                        color:
                          product.stock.status === 'In Stock'
                            ? '#4caf50'
                            : product.stock.status === 'Low Stock'
                            ? '#ff9800'
                            : '#f44336',
                        border: `1px solid ${
                          product.stock.status === 'In Stock'
                            ? '#4caf50'
                            : product.stock.status === 'Low Stock'
                            ? '#ff9800'
                            : '#f44336'
                        }`,
                      }}
                    />
                  </Stack>

                  {/* Description */}
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.9rem' },
                      lineHeight: 1.4,
                      display: '-webkit-box',
                      WebkitLineClamp: { xs: 2, sm: 2, md: 3 }, // More lines on larger screens
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      mb: { xs: 0.5, sm: 0.75, md: 1 },
                      wordBreak: 'break-word', // Better text wrapping
                    }}
                  >
                    {product.description?.en || 'High-quality automotive part designed for optimal performance and durability.'}
                  </Typography>

                  {/* Rating */}
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Rating
                      value={4.5}
                      precision={0.5}
                      size="small"
                      readOnly
                      sx={{
                        '& .MuiRating-iconFilled': {
                          color: '#ffd700',
                        },
                      }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      (4.5) • 127 reviews
                    </Typography>
                  </Stack>
                </Box>

                {/* Price and Actions Section */}
                <Box sx={{ mt: 'auto' }}> {/* Push to bottom */}
                  <Stack
                    direction={{ xs: 'column', sm: 'row' }}
                    alignItems={{ xs: 'flex-start', sm: 'center' }}
                    justifyContent="space-between"
                    spacing={{ xs: 1, sm: 0 }}
                    sx={{ mb: { xs: 0.5, sm: 1 } }}
                  >
                    <Box>
                      <Typography
                        variant="h5"
                        sx={{
                          color: 'primary.main',
                          fontWeight: 800,
                          fontSize: { xs: '1.1rem', sm: '1.2rem', md: '1.3rem' },
                          lineHeight: 1,
                        }}
                      >
                        ${product.priceUSD.toFixed(2)}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'text.secondary',
                          fontSize: '0.8rem',
                          textDecoration: 'line-through'
                        }}
                      >
                        ${(product.priceUSD * 1.2).toFixed(2)}
                      </Typography>
                    </Box>
                    <Chip
                      label="20% OFF"
                      size="small"
                      sx={{
                        bgcolor: 'error.main',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '0.75rem',
                      }}
                    />
                  </Stack>

                  {/* Action Buttons */}
                  <Stack
                    direction="row"
                    spacing={{ xs: 0.5, sm: 1 }}
                    className="list-actions"
                    sx={{
                      opacity: { xs: 1, md: 0.7 },
                      transform: { xs: 'translateX(0)', md: 'translateX(10px)' },
                      transition: 'all 0.3s ease',
                      mt: { xs: 1, sm: 0 } // Add margin top on mobile
                    }}
                  >
                    <Button
                      variant="contained"
                      startIcon={<ShoppingCartIcon />}
                      sx={{
                        background: 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)',
                        color: 'white',
                        fontWeight: 600,
                        borderRadius: 2,
                        textTransform: 'none',
                        fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
                        px: { xs: 1.5, sm: 2 },
                        py: { xs: 0.5, sm: 0.75 },
                        boxShadow: '0 2px 8px rgba(46, 192, 203, 0.3)',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #23A3AD 0%, #1e8e96 100%)',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 4px 12px rgba(46, 192, 203, 0.4)',
                        },
                        transition: 'all 0.2s ease',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        // Add to cart logic
                      }}
                    >
                      Add to Cart
                    </Button>

                  </Stack>
                </Box>
              </CardContent>
            </Card>
          </Box>
        ))}
      </Box>
    </Box>
  );
}

export default ProductListView;