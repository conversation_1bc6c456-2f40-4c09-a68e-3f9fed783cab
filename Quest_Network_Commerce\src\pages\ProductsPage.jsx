import React, { useState, useEffect } from 'react';
import { useSearchParams, useLocation } from 'react-router-dom';
import {
  Box,
  IconButton,
  useMediaQuery,
  useTheme,
  Drawer,
  Typography,
  Fade,
  Paper,
  Chip,
  Stack,
  Button,
  ButtonGroup,
  Tooltip
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import ViewListIcon from '@mui/icons-material/ViewList';
import TuneIcon from '@mui/icons-material/Tune';
import CloseIcon from '@mui/icons-material/Close';
import ClearIcon from '@mui/icons-material/Clear';
import FiltersSidebar from '../components/product/FiltersSidebar';
import ProductGridView from '../components/product/ProductGridView';
import ProductListView from '../components/product/ProductListView';
import ProductDetailPage from './ProductDetailPage';
import ImageSearchModal from '../components/product/ImageSearchModal';
import BarcodeScannerModal from '../components/product/BarcodeScannerModal';
import BulkOrderModal from '../components/product/BulkOrderModal';
import Header from '../components/layout/Header';

function ProductsPage() {
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const [viewMode, setViewMode] = useState('grid');
  const [imageSearchOpen, setImageSearchOpen] = useState(false);
  const [barcodeScannerOpen, setBarcodeScannerOpen] = useState(false);
  const [bulkOrderOpen, setBulkOrderOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [filtersExpanded, setFiltersExpanded] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [filters, setFilters] = useState({
    makes: [],
    models: [],
    years: [],
    categories: [],
    brands: [],
    materials: [],
    warranties: [],
    stockLocations: [],
    priceRange: [0, 1000],
    searchText: '',
    partType: '',
    includeSuperseded: false,
  });

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const toggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };

  const toggleFilters = () => {
    setFiltersExpanded((prev) => !prev);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(
      (v) => Array.isArray(v) ? v.length > 0 : v && v !== '' && v !== false && v !== 0 && v !== 'all'
    ).length;
  };

  const clearAllFilters = () => {
    setFilters({
      makes: [],
      models: [],
      years: [],
      categories: [],
      brands: [],
      materials: [],
      warranties: [],
      stockLocations: [],
      priceRange: [0, 1000],
      searchText: '',
      partType: '',
      includeSuperseded: false,
    });
  };

  useEffect(() => {
    if (isSmallScreen && sidebarOpen) {
      setSidebarOpen(false);
    }
  }, [filters, isSmallScreen]);

  // Handle URL search parameters on page load
  useEffect(() => {
    const searchQuery = searchParams.get('search');
    const makeFilter = searchParams.get('make');
    const modelFilter = searchParams.get('model');
    const categoryFilter = searchParams.get('category');

    if (searchQuery || makeFilter || modelFilter || categoryFilter) {
      setFilters((prev) => ({
        ...prev,
        searchText: searchQuery || '',
        makes: makeFilter ? [makeFilter] : [],
        models: modelFilter ? [modelFilter] : [],
        categories: categoryFilter ? [categoryFilter] : []
      }));
    }
  }, [searchParams]);

  // Listen to Header search changes via custom event
  useEffect(() => {
    const handleHeaderSearch = (e) => {
      setFilters((prev) => ({ ...prev, searchText: e.detail }));
    };
    window.addEventListener('header-search', handleHeaderSearch);
    return () => window.removeEventListener('header-search', handleHeaderSearch);
  }, []);

  return (
    <>
      <Header />
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #F5F7F9 0%, #E8F4F8 100%)',
          p: { xs: 0.5, sm: 1, md: 1.5, lg: 2 } // Responsive padding for all screen sizes
        }}
      >
        {selectedProduct ? (
          <Fade in={!!selectedProduct} timeout={200}>
            <Paper
              elevation={12}
              sx={{
                borderRadius: 2,
                overflow: 'hidden',
                background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                boxShadow: '0 8px 32px rgba(46, 192, 203, 0.15)',
                m: 1
              }}
            >
              <ProductDetailPage product={selectedProduct} setSelectedProduct={setSelectedProduct} />
            </Paper>
          </Fade>
        ) : (
          <Box sx={{
          width: '100%',
          maxWidth: '100%',
          mx: 'auto',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'stretch' // Changed from center to stretch for full width
        }}>
            {/* Enhanced Modern Header */}
            <Paper
              elevation={filtersExpanded || sidebarOpen ? 8 : 3}
              sx={{
                mb: { xs: 0.5, sm: 1, md: 1.5 },
                borderRadius: { xs: 1, sm: 2, md: 3 }, // Responsive border radius
                width: '100%',
                maxWidth: '100%',
                background: filtersExpanded || sidebarOpen
                  ? 'linear-gradient(135deg, rgba(46, 192, 203, 0.95) 0%, rgba(35, 163, 173, 0.95) 100%)'
                  : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                border: `2px solid ${filtersExpanded || sidebarOpen ? 'rgba(46, 192, 203, 0.3)' : 'rgba(46, 192, 203, 0.08)'}`,
                overflow: 'hidden',
                backdropFilter: filtersExpanded || sidebarOpen ? 'blur(10px)' : 'none',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                transform: filtersExpanded || sidebarOpen ? 'translateY(-2px)' : 'translateY(0)',
                color: filtersExpanded || sidebarOpen ? 'white' : 'inherit'
              }}
            >
              <Box sx={{ px: 2.5, py: 2 }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={2}>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: 2,
                        background: filtersExpanded || sidebarOpen
                          ? 'rgba(255, 255, 255, 0.2)'
                          : 'linear-gradient(45deg, #2EC0CB, #23A3AD)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: filtersExpanded || sidebarOpen
                          ? '0 4px 20px rgba(255, 255, 255, 0.3)'
                          : '0 4px 20px rgba(46, 192, 203, 0.4)',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <ViewModuleIcon sx={{
                        color: 'white',
                        fontSize: 20,
                        filter: filtersExpanded || sidebarOpen ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))' : 'none'
                      }} />
                    </Box>
                    <Box>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 700,
                          fontSize: '1.1rem',
                          display: { xs: 'none', sm: 'block' },
                          textShadow: filtersExpanded || sidebarOpen ? '0 2px 4px rgba(0,0,0,0.3)' : 'none'
                        }}
                      >
                        Product Catalog
                      </Typography>
                      {getActiveFiltersCount() > 0 && (
                        <Chip
                          size="small"
                          label={`${getActiveFiltersCount()} active filters`}
                          sx={{
                            height: 20,
                            fontSize: '0.7rem',
                            bgcolor: filtersExpanded || sidebarOpen
                              ? 'rgba(255, 255, 255, 0.2)'
                              : 'rgba(46, 192, 203, 0.1)',
                            color: filtersExpanded || sidebarOpen ? 'white' : 'primary.main',
                            mt: 0.5
                          }}
                        />
                      )}
                    </Box>
                  </Stack>

                  <Stack direction="row" alignItems="center" spacing={1.5}>
                    {/* Clear Filters Button */}
                    {getActiveFiltersCount() > 0 && (
                      <Button
                        onClick={clearAllFilters}
                        variant="outlined"
                        size="medium"
                        startIcon={<ClearIcon />}
                        sx={{
                          height: 40,
                          minWidth: isMobile ? 40 : 100,
                          px: isMobile ? 1 : 2,
                          fontSize: '0.85rem',
                          fontWeight: 500,
                          borderColor: filtersExpanded || sidebarOpen
                            ? 'rgba(255, 255, 255, 0.4)'
                            : 'error.main',
                          color: filtersExpanded || sidebarOpen ? 'white' : 'error.main',
                          '&:hover': {
                            bgcolor: filtersExpanded || sidebarOpen
                              ? 'rgba(255, 255, 255, 0.1)'
                              : 'rgba(244, 67, 54, 0.04)',
                            borderColor: filtersExpanded || sidebarOpen
                              ? 'rgba(255, 255, 255, 0.6)'
                              : 'error.dark',
                            transform: 'scale(1.02)'
                          },
                          transition: 'all 0.2s ease',
                          '& .MuiButton-startIcon': {
                            margin: isMobile ? 0 : undefined,
                            fontSize: '1rem'
                          }
                        }}
                      >
                        {!isMobile && 'Clear'}
                      </Button>
                    )}

                    {/* View Mode Toggle */}
                    <ButtonGroup
                      size="medium"
                      variant="outlined"
                      sx={{
                        height: 40,
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: 2,
                        boxShadow: '0 2px 8px rgba(46, 192, 203, 0.1)',
                        '& .MuiButton-root': {
                          borderColor: 'rgba(46, 192, 203, 0.3)',
                          color: 'text.primary',
                          minWidth: 40,
                          px: 1.5,
                          '&:not(:last-child)': {
                            borderRight: '1px solid rgba(46, 192, 203, 0.3)',
                          }
                        }
                      }}
                    >
                      <Tooltip title="Grid View" arrow>
                        <Button
                          onClick={() => setViewMode('grid')}
                          variant={viewMode === 'grid' ? 'contained' : 'outlined'}
                          sx={{
                            bgcolor: viewMode === 'grid' ? 'primary.main' : 'transparent',
                            color: viewMode === 'grid' ? 'white' : 'primary.main',
                            '&:hover': {
                              bgcolor: viewMode === 'grid' ? 'primary.dark' : 'rgba(46, 192, 203, 0.08)',
                              color: viewMode === 'grid' ? 'white' : 'primary.main',
                            },
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <ViewModuleIcon fontSize="small" />
                        </Button>
                      </Tooltip>
                      <Tooltip title="List View" arrow>
                        <Button
                          onClick={() => setViewMode('list')}
                          variant={viewMode === 'list' ? 'contained' : 'outlined'}
                          sx={{
                            bgcolor: viewMode === 'list' ? 'primary.main' : 'transparent',
                            color: viewMode === 'list' ? 'white' : 'primary.main',
                            '&:hover': {
                              bgcolor: viewMode === 'list' ? 'primary.dark' : 'rgba(46, 192, 203, 0.08)',
                              color: viewMode === 'list' ? 'white' : 'primary.main',
                            },
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <ViewListIcon fontSize="small" />
                        </Button>
                      </Tooltip>
                    </ButtonGroup>

                    {/* Enhanced Filters Toggle */}
                    <Button
                      onClick={isMobile ? toggleSidebar : toggleFilters}
                      variant={filtersExpanded || sidebarOpen ? "contained" : "outlined"}
                      size="large"
                      startIcon={filtersExpanded || sidebarOpen ? <CloseIcon /> : <TuneIcon />}
                      sx={{
                        height: 44,
                        minWidth: isMobile ? 44 : 120,
                        px: isMobile ? 1.5 : 3,
                        fontSize: '0.95rem',
                        fontWeight: 600,
                        bgcolor: filtersExpanded || sidebarOpen
                          ? 'rgba(255, 255, 255, 0.2)'
                          : 'transparent',
                        borderColor: filtersExpanded || sidebarOpen
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'primary.main',
                        borderWidth: 2,
                        color: filtersExpanded || sidebarOpen ? 'white' : 'primary.main',
                        '&:hover': {
                          bgcolor: filtersExpanded || sidebarOpen
                            ? 'rgba(255, 255, 255, 0.3)'
                            : 'rgba(46, 192, 203, 0.08)',
                          transform: 'scale(1.05)',
                          borderWidth: 2,
                          boxShadow: filtersExpanded || sidebarOpen
                            ? '0 8px 25px rgba(255, 255, 255, 0.3)'
                            : '0 8px 25px rgba(46, 192, 203, 0.3)'
                        },
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '& .MuiButton-startIcon': {
                          margin: isMobile ? 0 : undefined,
                          fontSize: '1.2rem'
                        }
                      }}
                    >
                      {!isMobile && (filtersExpanded || sidebarOpen ? 'Close Filters' : 'Open Filters')}
                    </Button>
                  </Stack>
                </Stack>

                {/* Enhanced Search Indicator */}
                {filters.searchText && (
                  <Fade in={!!filters.searchText} timeout={300}>
                    <Box sx={{ mt: 2 }}>
                      <Chip
                        icon={<SearchIcon />}
                        label={`Searching: "${filters.searchText}"`}
                        onDelete={() => setFilters(prev => ({ ...prev, searchText: '' }))}
                        deleteIcon={<CloseIcon />}
                        sx={{
                          height: 32,
                          fontSize: '0.8rem',
                          bgcolor: filtersExpanded || sidebarOpen
                            ? 'rgba(255, 255, 255, 0.2)'
                            : 'rgba(46, 192, 203, 0.1)',
                          color: filtersExpanded || sidebarOpen ? 'white' : 'primary.main',
                          border: `1px solid ${filtersExpanded || sidebarOpen
                            ? 'rgba(255, 255, 255, 0.3)'
                            : 'rgba(46, 192, 203, 0.3)'}`,
                          '& .MuiChip-icon': {
                            color: filtersExpanded || sidebarOpen ? 'white' : 'primary.main'
                          },
                          '& .MuiChip-deleteIcon': {
                            color: filtersExpanded || sidebarOpen ? 'white' : 'primary.main'
                          }
                        }}
                      />
                    </Box>
                  </Fade>
                )}
              </Box>
            </Paper>

            {/* Full Width Products Display with Backdrop */}
            <Box sx={{ position: 'relative' }}>
              {/* Backdrop Blur Overlay */}
              {(filtersExpanded || sidebarOpen) && (
                <Fade in={filtersExpanded || sidebarOpen} timeout={300}>
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      bgcolor: 'rgba(46, 192, 203, 0.1)',
                      backdropFilter: 'blur(8px)',
                      zIndex: 500,
                      borderRadius: 2
                    }}
                  />
                </Fade>
              )}

              <Paper
                elevation={filtersExpanded || sidebarOpen ? 1 : 2}
                sx={{
                  borderRadius: { xs: 1, sm: 2, md: 3 }, // Responsive border radius
                  width: '100%',
                  maxWidth: '100%',
                  background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                  border: '1px solid rgba(46, 192, 203, 0.08)',
                  overflow: 'hidden',
                  minHeight: { xs: 'calc(100vh - 100px)', sm: 'calc(100vh - 120px)', md: 'calc(100vh - 140px)' },
                  position: 'relative',
                  transition: 'all 0.3s ease',
                  filter: filtersExpanded || sidebarOpen ? 'brightness(0.95)' : 'brightness(1)',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                {viewMode === 'grid' ? (
                  <ProductGridView
                    setViewMode={setViewMode}
                    filters={filters}
                    isSmallScreen={isSmallScreen}
                    setSelectedProduct={setSelectedProduct}
                  />
                ) : (
                  <ProductListView
                    setViewMode={setViewMode}
                    filters={filters}
                    isSmallScreen={isSmallScreen}
                    setSelectedProduct={setSelectedProduct}
                  />
                )}
              </Paper>

              {/* Full Screen Desktop Filters Overlay */}
              {!isMobile && (
                <Fade in={filtersExpanded} timeout={400}>
                  <Box
                    sx={{
                      position: 'fixed',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      zIndex: 1300,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(0, 0, 0, 0.7)',
                      backdropFilter: 'blur(12px)',
                      p: 3
                    }}
                    onClick={(e) => {
                      if (e.target === e.currentTarget) toggleFilters();
                    }}
                  >
                    <Paper
                      elevation={24}
                      sx={{
                        width: '90vw',
                        maxWidth: 1200,
                        height: '90vh',
                        borderRadius: 4,
                        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                        border: '3px solid rgba(46, 192, 203, 0.3)',
                        boxShadow: '0 30px 80px rgba(46, 192, 203, 0.4)',
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column',
                        transform: 'scale(1)',
                        transition: 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)'
                      }}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {/* Full Screen Header */}
                      <Box
                        sx={{
                          background: 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)',
                          color: 'white',
                          p: 3,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          boxShadow: '0 4px 20px rgba(46, 192, 203, 0.3)'
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Box
                            sx={{
                              width: 48,
                              height: 48,
                              borderRadius: 2,
                              background: 'rgba(255, 255, 255, 0.2)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              backdropFilter: 'blur(10px)'
                            }}
                          >
                            <TuneIcon sx={{ fontSize: 24, color: 'white' }} />
                          </Box>
                          <Box>
                            <Typography variant="h4" sx={{ fontWeight: 700, fontSize: '1.8rem', mb: 0.5 }}>
                              Filter Products
                            </Typography>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>
                              Refine your search to find exactly what you need
                            </Typography>
                          </Box>
                        </Box>

                        <Stack direction="row" spacing={2}>
                          {getActiveFiltersCount() > 0 && (
                            <Chip
                              label={`${getActiveFiltersCount()} Active`}
                              sx={{
                                bgcolor: 'rgba(255, 255, 255, 0.2)',
                                color: 'white',
                                fontWeight: 600,
                                backdropFilter: 'blur(10px)'
                              }}
                            />
                          )}
                          <IconButton
                            onClick={toggleFilters}
                            sx={{
                              color: 'white',
                              bgcolor: 'rgba(255, 255, 255, 0.2)',
                              width: 48,
                              height: 48,
                              '&:hover': {
                                bgcolor: 'rgba(255, 255, 255, 0.3)',
                                transform: 'rotate(90deg) scale(1.1)'
                              },
                              transition: 'all 0.3s ease'
                            }}
                          >
                            <CloseIcon />
                          </IconButton>
                        </Stack>
                      </Box>

                      {/* Full Screen Filters Content */}
                      <Box
                        sx={{
                          flex: 1,
                          overflow: 'hidden',
                          display: 'flex',
                          flexDirection: 'column'
                        }}
                      >
                        <FiltersSidebar
                          setImageSearchOpen={setImageSearchOpen}
                          setBarcodeScannerOpen={setBarcodeScannerOpen}
                          setBulkOrderOpen={setBulkOrderOpen}
                          filters={filters}
                          setFilters={setFilters}
                          fullScreen={true}
                        />
                      </Box>
                    </Paper>
                  </Box>
                </Fade>
              )}
            </Box>

            {/* Full Screen Mobile Filters */}
            {isMobile && (
              <Drawer
                anchor="bottom"
                open={sidebarOpen}
                onClose={toggleSidebar}
                slotProps={{
                  paper: {
                    sx: {
                      height: '100vh',
                      background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                      borderRadius: 0,
                      boxShadow: 'none',
                      border: 'none',
                      display: 'flex',
                      flexDirection: 'column'
                    }
                  }
                }}
              >
                {/* Full Screen Mobile Header */}
                <Box
                  sx={{
                    background: 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)',
                    color: 'white',
                    p: 2,
                    boxShadow: '0 4px 20px rgba(46, 192, 203, 0.3)',
                    position: 'relative',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 8,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: 40,
                      height: 4,
                      bgcolor: 'rgba(255, 255, 255, 0.5)',
                      borderRadius: 2
                    }
                  }}
                >
                  <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mt: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: 2,
                          background: 'rgba(255, 255, 255, 0.2)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backdropFilter: 'blur(10px)'
                        }}
                      >
                        <TuneIcon sx={{ fontSize: 20, color: 'white' }} />
                      </Box>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 700, fontSize: '1.2rem' }}>
                          Filter Products
                        </Typography>
                        {getActiveFiltersCount() > 0 && (
                          <Typography variant="caption" sx={{ opacity: 0.9 }}>
                            {getActiveFiltersCount()} filters active
                          </Typography>
                        )}
                      </Box>
                    </Box>
                    <IconButton
                      onClick={toggleSidebar}
                      sx={{
                        color: 'white',
                        bgcolor: 'rgba(255, 255, 255, 0.2)',
                        width: 40,
                        height: 40,
                        '&:hover': {
                          bgcolor: 'rgba(255, 255, 255, 0.3)',
                          transform: 'rotate(90deg) scale(1.1)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <CloseIcon />
                    </IconButton>
                  </Stack>
                </Box>

                {/* Full Screen Mobile Filters Content */}
                <Box sx={{
                  flex: 1,
                  overflow: 'hidden',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <FiltersSidebar
                    setImageSearchOpen={setImageSearchOpen}
                    setBarcodeScannerOpen={setBarcodeScannerOpen}
                    setBulkOrderOpen={setBulkOrderOpen}
                    filters={filters}
                    setFilters={setFilters}
                    fullScreen={true}
                  />
                </Box>
              </Drawer>
            )}
          </Box>
        )}

        {/* Modals */}
        <ImageSearchModal open={imageSearchOpen} onClose={() => setImageSearchOpen(false)} />
        <BarcodeScannerModal open={barcodeScannerOpen} onClose={() => setBarcodeScannerOpen(false)} />
        <BulkOrderModal open={bulkOrderOpen} onClose={() => setBulkOrderOpen(false)} />
      </Box>
    </>
  );
}

export default ProductsPage;