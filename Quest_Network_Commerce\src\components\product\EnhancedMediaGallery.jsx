import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Stack,
  IconButton,
  Typography,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  Chip,
  Tooltip,
  Fab,
  Badge,
  Card,
  CardMedia,
  useTheme,
  useMediaQuery,
  Skeleton,
  Fade
} from '@mui/material';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import ThreeDRotationIcon from '@mui/icons-material/ThreeDRotation';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import CloseIcon from '@mui/icons-material/Close';

import Product3DViewer from './Product3DViewer';

function EnhancedMediaGallery({ product, selectedMedia, onMediaChange }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [showFullscreen, setShowFullscreen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [show360View, setShow360View] = useState(false);
  const [showARView, setShowARView] = useState(false);
  const [show3DViewer, setShow3DViewer] = useState(false);
  const [rotation360, setRotation360] = useState(0);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const imageRef = useRef(null);
  const videoRef = useRef(null);
  const containerRef = useRef(null);

  // Helper function to normalize image paths
  const normalizeImagePath = (imagePath) => {
    if (!imagePath) return '/src/assets/images/placeholder.jpg';

    // If already starts with /src/assets, return as is
    if (imagePath.startsWith('/src/assets/')) return imagePath;

    // Handle various legacy path formats
    if (imagePath.startsWith('./images/') || imagePath.startsWith('./Assets/Images/')) {
      const filename = imagePath.split('/').pop();
      return `/src/assets/images/${filename}`;
    }

    // Handle relative paths
    if (imagePath.startsWith('./')) {
      const filename = imagePath.split('/').pop();
      return `/src/assets/images/${filename}`;
    }

    // If it's just a filename, prepend the assets path
    if (!imagePath.includes('/')) {
      return `/src/assets/images/${imagePath}`;
    }

    return imagePath;
  };

  // Enhanced media data structure
  const mediaItems = [
    // Main product images
    ...(product.images || []).map((img, index) => ({
      id: `img_${index}`,
      type: 'image',
      url: normalizeImagePath(img),
      highResUrl: normalizeImagePath(img).replace('.jpg', '_hd.jpg'), // Assume HD versions exist
      thumbnail: normalizeImagePath(img),
      title: `${product.name?.en} - View ${index + 1}`,
      category: 'product'
    })),
    
    // Product videos
    ...(product.videos || []).map((video, index) => ({
      id: `video_${index}`,
      type: 'video',
      url: video.url?.startsWith('/src/assets/') ? video.url : `/src/assets/videos/${video.url?.split('/').pop() || 'placeholder.mp4'}`,
      thumbnail: normalizeImagePath(video.thumbnail),
      title: video.title,
      duration: video.duration,
      category: 'demo'
    })),
    
    // 360° views
    ...(product.view360 || []).map((view, index) => ({
      id: `360_${index}`,
      type: '360',
      frames: view.frames,
      thumbnail: view.frames[0],
      title: `360° View ${index + 1}`,
      category: '360'
    })),
    
    // 3D models
    ...(product.models3D || []).map((model, index) => ({
      id: `3d_${index}`,
      type: '3d',
      url: model.url,
      thumbnail: model.thumbnail,
      title: `3D Model ${index + 1}`,
      category: '3d'
    })),
    
    // User-generated content
    ...(product.userMedia || []).map((media, index) => ({
      id: `user_${index}`,
      type: media.type,
      url: media.url,
      thumbnail: media.thumbnail,
      title: media.title,
      author: media.author,
      category: 'user'
    }))
  ];

  const currentMedia = mediaItems[selectedMedia] || mediaItems[0];

  // Image loading and error handling
  useEffect(() => {
    if (currentMedia?.type === 'image') {
      setImageLoading(true);
      setImageError(false);
      setCurrentImageIndex(selectedMedia);
    }
  }, [selectedMedia, currentMedia]);

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 5));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.5, 1));
    if (zoomLevel <= 1.5) {
      setPanPosition({ x: 0, y: 0 });
    }
  };

  const handleMouseMove = (e) => {
    if (zoomLevel > 1 && imageRef.current) {
      const rect = imageRef.current.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width - 0.5) * 100;
      const y = ((e.clientY - rect.top) / rect.height - 0.5) * 100;
      setPanPosition({ x: -x, y: -y });
    }
  };

  const handle360Drag = (e) => {
    if (show360View) {
      const deltaX = e.movementX || 0;
      setRotation360(prev => (prev + deltaX) % 360);
    }
  };

  const toggleVideo = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'demo': return <VideoLibraryIcon />;
      case '360': return <ThreeDRotationIcon />;
      case '3d': return <ViewInArIcon />;
      case 'user': return <CameraAltIcon />;
      default: return null;
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'demo': return 'primary';
      case '360': return 'secondary';
      case '3d': return 'success';
      case 'user': return 'warning';
      default: return 'default';
    }
  };

  const renderMainMedia = () => {
    if (!currentMedia) {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'text.secondary',
            bgcolor: 'grey.100'
          }}
        >
          <CameraAltIcon sx={{ fontSize: 48, mb: 1 }} />
          <Typography variant="body2">No media available</Typography>
        </Box>
      );
    }

    switch (currentMedia.type) {
      case 'image':
        return (
          <Box
            ref={containerRef}
            sx={{
              position: 'relative',
              width: '100%',
              height: '100%',
              overflow: 'hidden',
              cursor: zoomLevel > 1 ? 'move' : 'zoom-in'
            }}
            onMouseMove={handleMouseMove}
            onClick={() => zoomLevel === 1 && handleZoomIn()}
          >
            {/* Loading Skeleton */}
            {imageLoading && (
              <Skeleton
                variant="rectangular"
                width="100%"
                height="100%"
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  zIndex: 1
                }}
              />
            )}

            {/* Error Fallback */}
            {imageError && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  color: 'text.secondary',
                  bgcolor: 'grey.100'
                }}
              >
                <CameraAltIcon sx={{ fontSize: 48, mb: 1 }} />
                <Typography variant="body2">Image not available</Typography>
              </Box>
            )}

            {/* Main Image with Fade Transition */}
            <Box
              component="img"
              ref={imageRef}
              src={showFullscreen ? currentMedia.highResUrl : currentMedia.url}
              alt={currentMedia.title}
              onLoad={handleImageLoad}
              onError={handleImageError}
              sx={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                transform: `scale(${zoomLevel}) translate(${panPosition.x}px, ${panPosition.y}px)`,
                transition: zoomLevel === 1 ? 'transform 0.3s ease' : 'none',
                maxWidth: '100%',
                maxHeight: '100%',
                opacity: imageLoading ? 0.5 : 1,
                filter: imageError ? 'grayscale(100%)' : 'none'
              }}
            />
            
            {/* Zoom Controls */}
            {zoomLevel > 1 && (
              <Box sx={{ position: 'absolute', top: 16, right: 16 }}>
                <Stack spacing={1}>
                  <IconButton
                    onClick={handleZoomIn}
                    sx={{ bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                    disabled={zoomLevel >= 5}
                  >
                    <ZoomInIcon />
                  </IconButton>
                  <IconButton
                    onClick={handleZoomOut}
                    sx={{ bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                  >
                    <ZoomOutIcon />
                  </IconButton>
                </Stack>
              </Box>
            )}
          </Box>
        );

      case 'video':
        return (
          <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
            <video
              ref={videoRef}
              src={currentMedia.url}
              poster={currentMedia.thumbnail}
              style={{ width: '100%', height: '100%', objectFit: 'contain' }}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              controls={!isMobile}
            />
            
            {/* Custom Video Controls for Mobile */}
            {isMobile && (
              <Box sx={{
                position: 'absolute',
                bottom: 16,
                left: 16,
                right: 16,
                display: 'flex',
                justifyContent: 'center',
                gap: 2
              }}>
                <IconButton
                  onClick={toggleVideo}
                  sx={{ bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                >
                  {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
                </IconButton>
                <IconButton
                  onClick={toggleMute}
                  sx={{ bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                >
                  {isMuted ? <VolumeOffIcon /> : <VolumeUpIcon />}
                </IconButton>
              </Box>
            )}
          </Box>
        );

      case '360':
        return (
          <Box
            sx={{
              position: 'relative',
              width: '100%',
              height: '100%',
              cursor: 'grab',
              '&:active': { cursor: 'grabbing' }
            }}
            onMouseMove={handle360Drag}
          >
            <img
              src={currentMedia.frames[Math.floor((rotation360 / 360) * currentMedia.frames.length)]}
              alt={currentMedia.title}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain'
              }}
            />
            
            {/* 360° Indicator */}
            <Box sx={{
              position: 'absolute',
              top: 16,
              left: 16,
              bgcolor: 'rgba(0,0,0,0.7)',
              color: 'white',
              px: 2,
              py: 1,
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <ThreeDRotationIcon fontSize="small" />
              <Typography variant="caption">360° View - Drag to rotate</Typography>
            </Box>
          </Box>
        );

      case '3d':
        return (
          <Box sx={{
            position: 'relative',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'grey.100'
          }}>
            <Stack spacing={2} alignItems="center">
              <ViewInArIcon sx={{ fontSize: 64, color: 'primary.main' }} />
              <Typography variant="h6">3D Model Viewer</Typography>
              <Typography variant="body2" color="text.secondary" textAlign="center">
                Interactive 3D model with rotation, zoom, and material options
              </Typography>
              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  startIcon={<ThreeDRotationIcon />}
                  onClick={() => setShow3DViewer(true)}
                >
                  Open 3D Viewer
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<ViewInArIcon />}
                  onClick={() => setShowARView(true)}
                >
                  View in AR
                </Button>
              </Stack>
            </Stack>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Paper
      elevation={3}
      sx={{
        height: { xs: 'auto', md: '600px' },
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)'
      }}
    >
      {/* Main Media Display */}
      <Box
        sx={{
          flex: 1,
          position: 'relative',
          minHeight: { xs: '400px', sm: '450px', md: '500px' },
          maxHeight: { xs: '400px', sm: '450px', md: '500px' },
          aspectRatio: { xs: '4/3', md: '16/10' },
          bgcolor: 'grey.50',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {renderMainMedia()}
        
        {/* Media Type Badge */}
        {currentMedia && (
          <Chip
            icon={getCategoryIcon(currentMedia.category)}
            label={currentMedia.category.toUpperCase()}
            color={getCategoryColor(currentMedia.category)}
            size="small"
            sx={{
              position: 'absolute',
              top: 16,
              left: 16,
              bgcolor: 'rgba(255,255,255,0.9)'
            }}
          />
        )}
        
        {/* Fullscreen Button */}
        <IconButton
          onClick={() => setShowFullscreen(true)}
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            bgcolor: 'rgba(0,0,0,0.7)',
            color: 'white',
            '&:hover': { bgcolor: 'rgba(0,0,0,0.8)' }
          }}
        >
          <FullscreenIcon />
        </IconButton>

        {/* AR/3D Action Buttons */}
        <Stack
          spacing={1}
          sx={{
            position: 'absolute',
            bottom: 16,
            right: 16
          }}
        >
          {product.models3D && (
            <Fab
              size="small"
              color="primary"
              onClick={() => setShow3DViewer(true)}
            >
              <ThreeDRotationIcon />
            </Fab>
          )}
          
          {product.arSupported && (
            <Fab
              size="small"
              color="secondary"
              onClick={() => setShowARView(true)}
            >
              <ViewInArIcon />
            </Fab>
          )}
        </Stack>
      </Box>

      {/* Media Thumbnails */}
      {mediaItems.length > 1 && (
        <Box sx={{ p: { xs: 1, md: 2 }, flexShrink: 0 }}>
          <Stack direction="row" spacing={1} sx={{ overflowX: 'auto' }}>
            {mediaItems.map((media, index) => (
              <Card
                key={media.id}
                onClick={() => onMediaChange(index)}
                sx={{
                  minWidth: { xs: 60, md: 80 },
                  height: { xs: 60, md: 80 },
                  cursor: 'pointer',
                  border: selectedMedia === index ? 2 : 1,
                  borderColor: selectedMedia === index ? 'primary.main' : 'divider',
                  position: 'relative',
                  '&:hover': { borderColor: 'primary.main' }
                }}
              >
                <CardMedia
                  component="img"
                  image={media.thumbnail}
                  alt={media.title}
                  sx={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
                
                {/* Media Type Indicator */}
                {media.type !== 'image' && (
                  <Box sx={{
                    position: 'absolute',
                    top: 4,
                    right: 4,
                    bgcolor: 'rgba(0,0,0,0.7)',
                    borderRadius: '50%',
                    p: 0.5
                  }}>
                    {getCategoryIcon(media.category)}
                  </Box>
                )}
                
                {/* User Content Badge */}
                {media.category === 'user' && (
                  <Badge
                    badgeContent="U"
                    color="warning"
                    sx={{
                      position: 'absolute',
                      top: -4,
                      right: -4
                    }}
                  />
                )}
              </Card>
            ))}
          </Stack>
        </Box>
      )}

      {/* Fullscreen Dialog */}
      <Dialog
        open={showFullscreen}
        onClose={() => setShowFullscreen(false)}
        maxWidth={false}
        fullScreen
        sx={{ '& .MuiDialog-paper': { bgcolor: 'black' } }}
      >
        <DialogContent sx={{ p: 0, position: 'relative' }}>
          <Box sx={{ width: '100vw', height: '100vh' }}>
            {renderMainMedia()}
          </Box>
        </DialogContent>
        <DialogActions sx={{ position: 'absolute', top: 16, right: 16 }}>
          <IconButton
            onClick={() => setShowFullscreen(false)}
            sx={{ color: 'white', bgcolor: 'rgba(0,0,0,0.7)' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogActions>
      </Dialog>

      {/* AR View Dialog */}
      <Dialog
        open={showARView}
        onClose={() => setShowARView(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogContent>
          <Stack spacing={3} alignItems="center" sx={{ py: 4 }}>
            <ViewInArIcon sx={{ fontSize: 80, color: 'primary.main' }} />
            <Typography variant="h5" textAlign="center">
              Augmented Reality View
            </Typography>
            <Typography variant="body1" color="text.secondary" textAlign="center">
              Point your camera at a flat surface to see how this product would look in your space.
            </Typography>
            <Button
              variant="contained"
              size="large"
              startIcon={<ViewInArIcon />}
              onClick={() => {
                // AR implementation would go here
                alert('AR functionality would be implemented here using WebXR or AR.js');
              }}
            >
              Start AR Experience
            </Button>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowARView(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* 3D Viewer Dialog */}
      <Product3DViewer
        product={product}
        open={show3DViewer}
        onClose={() => setShow3DViewer(false)}
      />
    </Paper>
  );
}

export default EnhancedMediaGallery;
