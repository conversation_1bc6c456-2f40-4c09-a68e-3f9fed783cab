{"version": 3, "sources": ["../../@mui/icons-material/esm/ChatBubbleOutlineOutlined.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 14H6l-2 2V4h16z\"\n}), 'ChatBubbleOutlineOutlined');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,oCAAQ,kBAA2B,mBAAAA,KAAK,QAAQ;AAAA,EACrD,GAAG;AACL,CAAC,GAAG,2BAA2B;", "names": ["_jsx"]}