import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  IconButton,
  Chip,
  Rating,
  Divider,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Card,
  CardContent,
  CardMedia,
  Stack,
  Badge,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  useTheme,
  useMediaQuery,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';

// Icons
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import ShareIcon from '@mui/icons-material/Share';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import FlashOnIcon from '@mui/icons-material/FlashOn';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import StoreIcon from '@mui/icons-material/Store';
import VerifiedIcon from '@mui/icons-material/Verified';
import StarIcon from '@mui/icons-material/Star';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoIcon from '@mui/icons-material/Info';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import ReportIcon from '@mui/icons-material/Report';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import BuildIcon from '@mui/icons-material/Build';
import SecurityIcon from '@mui/icons-material/Security';
import AssignmentIcon from '@mui/icons-material/Assignment';
import BusinessIcon from '@mui/icons-material/Business';

// Data imports
import { masterProductList } from '../data/masterProductList';
import { bundleList } from '../data/bundleList';
import { deliveryPromiseData } from '../data/delivery';
import { reviewsData, reviewStats as reviewStatsData } from '../data/reviews';
import { faqData, faqStats } from '../data/faq';
import { comparisonData, similarProducts, comparisonSuggestions } from '../data/productComparison';
import ProductComparison from '../components/product/ProductComparison';
import EnhancedMediaGallery from '../components/product/EnhancedMediaGallery';

function ProductDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [product, setProduct] = useState(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [variantSelections, setVariantSelections] = useState({});
  const [availableVariants, setAvailableVariants] = useState([]);
  const [variantTypes, setVariantTypes] = useState([]);
  const [variantLoading, setVariantLoading] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [pinCode, setPinCode] = useState('');
  const [deliveryInfo, setDeliveryInfo] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [showImageDialog, setShowImageDialog] = useState(false);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isGift, setIsGift] = useState(false);
  const [giftMessage, setGiftMessage] = useState('');
  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState(0);
  const [showCompatibilityChecker, setShowCompatibilityChecker] = useState(false);
  const [vehicleInfo, setVehicleInfo] = useState({
    make: '',
    model: '',
    year: '',
    engine: ''
  });

  // Reviews and FAQ state
  const [reviews, setReviews] = useState([]);
  const [reviewStats, setReviewStats] = useState(null);
  const [faqList, setFaqList] = useState([]);
  const [showWriteReview, setShowWriteReview] = useState(false);
  const [showAskQuestion, setShowAskQuestion] = useState(false);
  const [reviewFilters, setReviewFilters] = useState({
    rating: 'all',
    verified: 'all',
    sortBy: 'newest'
  });
  const [newReview, setNewReview] = useState({
    rating: 5,
    title: '',
    comment: '',
    pros: '',
    cons: '',
    wouldRecommend: true,
    attachments: []
  });
  const [newQuestion, setNewQuestion] = useState({
    question: '',
    category: 'General'
  });

  // Product comparison state
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonProducts, setComparisonProducts] = useState([]);

  // Initialize product data
  useEffect(() => {
    if (id && masterProductList && masterProductList.length > 0) {
      const productId = parseInt(id);
      const productData = masterProductList.find(p => p.id === productId);

      if (productData) {
        setProduct(productData);
        setQuantity(productData.minOrderQty || productData.standardPackingQty || 1);
        // Initialize variant data
        if (productData.variants && productData.variants.length > 0) {
          initializeVariants(productData);
        }

        // Load reviews and FAQ data with safety checks
        try {
          const productReviews = (reviewsData && reviewsData[productId]) ? reviewsData[productId] : [];
          const productReviewStats = (reviewStatsData && reviewStatsData[productId]) ? reviewStatsData[productId] : null;
          const productFaq = (faqData && faqData[productId]) ? faqData[productId] : [];

          setReviews(productReviews);
          setReviewStats(productReviewStats);
          setFaqList(productFaq);
        } catch (error) {
          console.error('Error loading reviews/FAQ data:', error);
          setReviews([]);
          setReviewStats(null);
          setFaqList([]);
        }
      }
    }
  }, [id]);

  // Variant detection and management functions
  const initializeVariants = (productData) => {
    if (!productData.variants || productData.variants.length === 0) {
      setAvailableVariants([]);
      setVariantTypes([]);
      setSelectedVariant(null);
      setVariantSelections({});
      return;
    }

    setAvailableVariants(productData.variants);

    // Extract variant types from the variants
    const types = extractVariantTypes(productData.variants);
    setVariantTypes(types);

    // Set default selections (first option for each type)
    const defaultSelections = {};
    types.forEach(type => {
      const firstVariantWithType = productData.variants.find(v => v[type.key] !== undefined);
      if (firstVariantWithType) {
        defaultSelections[type.key] = firstVariantWithType[type.key];
      }
    });
    setVariantSelections(defaultSelections);

    // Find and set the default variant
    const defaultVariant = findMatchingVariant(productData.variants, defaultSelections);
    setSelectedVariant(defaultVariant || productData.variants[0]);
  };

  const extractVariantTypes = (variants) => {
    const typeSet = new Set();
    const types = [];

    variants.forEach(variant => {
      Object.keys(variant).forEach(key => {
        if (!typeSet.has(key) && !['id', 'sku', 'price', 'priceUSD', 'stock', 'images', 'availability'].includes(key)) {
          typeSet.add(key);
          types.push({
            key,
            label: formatVariantLabel(key),
            options: getUniqueOptions(variants, key)
          });
        }
      });
    });

    return types;
  };

  const getUniqueOptions = (variants, key) => {
    const options = [...new Set(variants.map(v => v[key]).filter(Boolean))];
    return options.map(option => ({
      value: option,
      label: option,
      available: variants.some(v => v[key] === option && (v.stock > 0 || v.availability !== 'out-of-stock'))
    }));
  };

  const formatVariantLabel = (key) => {
    return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  };

  const findMatchingVariant = (variants, selections) => {
    return variants.find(variant => {
      return Object.keys(selections).every(key => variant[key] === selections[key]);
    });
  };

  const handleVariantChange = (variantType, value) => {
    setVariantLoading(true);

    const newSelections = {
      ...variantSelections,
      [variantType]: value
    };

    setVariantSelections(newSelections);

    // Find matching variant
    const matchingVariant = findMatchingVariant(availableVariants, newSelections);
    if (matchingVariant) {
      setSelectedVariant(matchingVariant);

      // Update product images if variant has specific images
      if (matchingVariant.images && matchingVariant.images.length > 0) {
        setSelectedImage(0);
      }
    }

    setTimeout(() => setVariantLoading(false), 300);
  };

  // Handle Add to Cart with variant information
  const handleAddToCart = () => {
    // Check if selected variant is in stock
    if (selectedVariant && selectedVariant.stock <= 0) {
      setSnackbarMessage('Selected variant is out of stock. Please choose a different option.');
      setShowSnackbar(true);
      return;
    }

    const cartItem = {
      productId: product.id,
      name: product.name.en,
      price: selectedVariant?.priceUSD || product.priceUSD,
      quantity: quantity,
      image: selectedVariant?.images?.[0] || product.images?.[0] || product.imageUrl,
      sku: selectedVariant?.sku || product.partNumber,
      variant: selectedVariant ? {
        id: selectedVariant.id,
        selections: variantSelections,
        displayName: Object.entries(variantSelections)
          .map(([key, value]) => `${formatVariantLabel(key)}: ${value}`)
          .join(', ')
      } : null,
      giftWrap: isGift,
      giftMessage: isGift ? giftMessage : null
    };

    // Here you would typically dispatch to a cart context or state management
    console.log('Adding to cart:', cartItem);

    setSnackbarMessage(
      selectedVariant
        ? `Added ${product.name.en} (${cartItem.variant.displayName}) to cart successfully!`
        : `Added ${product.name.en} to cart successfully!`
    );
    setShowSnackbar(true);
  };

  // Handle delivery check
  const handleDeliveryCheck = () => {
    if (pinCode && product) {
      const deliveryData = deliveryPromiseData.find(
        d => d.pin === pinCode && d.productPartNumber === product.partNumber
      );
      setDeliveryInfo(deliveryData);
    }
  };

  // Calculate pricing with variant support
  const calculatePricing = () => {
    if (!product) return {};

    const basePrice = selectedVariant?.priceUSD || product.priceUSD;
    const totalPrice = basePrice * quantity;
    const discount = product.bulkPricing?.find(bp => quantity >= bp.minQty)?.discount || 0;
    const discountAmount = totalPrice * discount;
    const finalPrice = totalPrice - discountAmount;
    const tax = finalPrice * 0.08; // Assuming 8% tax
    const loyaltyPoints = Math.round(finalPrice * 0.01);
    const giftWrapCost = isGift ? (product.giftWrapPrice || 5) : 0;

    return {
      basePrice,
      totalPrice,
      discount: discount * 100,
      discountAmount,
      finalPrice: finalPrice + giftWrapCost,
      tax,
      loyaltyPoints,
      giftWrapCost
    };
  };

  if (!product) {
    return (
      <Container maxWidth="xl" sx={{ py: { xs: 1, md: 2 } }}>
        <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="400px" sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>Product not found</Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            The requested product (ID: {id}) could not be found.
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/products')}
            sx={{ mt: 2 }}
          >
            Back to Products
          </Button>
        </Box>
      </Container>
    );
  }

  const pricing = calculatePricing();
  const productBundles = bundleList.filter(bundle => bundle.partNumber === product.partNumber);

  return (
    <Container maxWidth="xl" sx={{ py: { xs: 1, md: 2 } }}>
      {/* Back Navigation */}
      <Box mb={2}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/products')}
          sx={{ color: 'text.secondary' }}
        >
          Back to Products
        </Button>
      </Box>

      {/* Main Product Layout */}
      <Box sx={{
        display: { xs: 'flex', md: 'grid' },
        flexDirection: { xs: 'column' },
        gridTemplateColumns: { md: '1fr 1fr' },
        gap: 3,
        alignItems: { xs: 'stretch', md: 'start' }
      }}>
        {/* Left Column - Enhanced Media Gallery (50% width on desktop) */}
        <Box sx={{ width: { xs: '100%', md: 'auto' } }}>
          <EnhancedMediaGallery
            product={product}
            selectedMedia={selectedImage}
            onMediaChange={setSelectedImage}
            selectedVariant={selectedVariant}
          />

          {/* Action Buttons */}
          <Paper elevation={1} sx={{ mt: 2, p: 2, borderRadius: 2 }}>
            <Stack direction="row" spacing={1} justifyContent="center">
              <Tooltip title={isWishlisted ? "Remove from Wishlist" : "Add to Wishlist"}>
                <IconButton
                  onClick={() => setIsWishlisted(!isWishlisted)}
                  color={isWishlisted ? "primary" : "default"}
                >
                  {isWishlisted ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                </IconButton>
              </Tooltip>
              <Tooltip title="Share Product">
                <IconButton>
                  <ShareIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          </Paper>
        </Box>

        {/* Right Column - Product Information (50% width on desktop) */}
        <Box sx={{
          width: { xs: '100%', md: 'auto' },
          display: 'flex',
          flexDirection: 'column',
          height: { xs: 'auto', md: '700px' },
          overflowY: { md: 'auto' }
        }}>
          {/* Product Header Information */}
          <Paper elevation={2} sx={{ p: 3, mb: 2, borderRadius: 2 }}>
            <Stack spacing={2}>
              {/* Product Name & Brand */}
              <Box>
                <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
                  {product.name.en}
                </Typography>
                <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
                  <Chip
                    label={product.brand?.en || 'Brand'}
                    color="primary"
                    variant="outlined"
                    size="small"
                  />
                  <Typography variant="body2" color="text.secondary">
                    Part #: {selectedVariant?.sku || product.partNumber}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    SKU: {product.sku || product.partNumber}
                  </Typography>
                  {product.socialProofMetrics?.purchases && (
                    <Chip
                      icon={<VerifiedIcon />}
                      label={`${product.socialProofMetrics.purchases} purchases`}
                      size="small"
                      color="success"
                      variant="outlined"
                    />
                  )}
                </Stack>
              </Box>

              {/* Rating & Reviews */}
              <Box>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Rating value={product.rating || 0} precision={0.1} readOnly />
                  <Typography variant="body2" color="text.secondary">
                    ({product.reviews?.length || 0} reviews)
                  </Typography>
                </Stack>
              </Box>

              {/* Stock Status */}
              <Box>
                <Stack direction="row" spacing={1} alignItems="center">
                  {(() => {
                    const variantStock = selectedVariant?.stock;
                    const productStock = product.stock;
                    const isInStock = variantStock ?
                      (variantStock > 0 || selectedVariant?.availability === 'in-stock') :
                      (productStock?.status === 'In Stock' || productStock?.quantity > 0);
                    const stockQuantity = variantStock || productStock?.quantity;

                    return isInStock ? (
                      <>
                        <CheckCircleIcon color="success" fontSize="small" />
                        <Typography color="success.main" fontWeight={600}>
                          In Stock
                        </Typography>
                        {stockQuantity && (
                          <Typography variant="body2" color="text.secondary">
                            ({stockQuantity} available)
                          </Typography>
                        )}
                      </>
                    ) : (
                      <>
                        <WarningIcon color="warning" fontSize="small" />
                        <Typography color="warning.main" fontWeight={600}>
                          {selectedVariant?.availability || productStock?.status || 'Out of Stock'}
                        </Typography>
                      </>
                    );
                  })()}
                </Stack>
              </Box>
            </Stack>
          </Paper>

          {/* Pricing & Purchase Options */}
          <Paper elevation={2} sx={{ p: 3, mb: 2, borderRadius: 2 }}>
            <Stack spacing={3}>
              {/* Pricing Information */}
              <Box>
                <Typography variant="h5" color="primary.main" fontWeight={700} gutterBottom>
                  ${pricing.finalPrice?.toFixed(2)}
                </Typography>

                {pricing.discount > 0 && (
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Typography
                      variant="body1"
                      sx={{ textDecoration: 'line-through' }}
                      color="text.secondary"
                    >
                      ${pricing.totalPrice?.toFixed(2)}
                    </Typography>
                    <Chip
                      label={`${pricing.discount}% OFF`}
                      color="error"
                      size="small"
                      sx={{ fontWeight: 600 }}
                    />
                  </Stack>
                )}

                {/* Price Breakdown */}
                <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Stack spacing={1}>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2">Base Price (x{quantity})</Typography>
                      <Typography variant="body2">${pricing.totalPrice?.toFixed(2)}</Typography>
                    </Stack>
                    {pricing.discountAmount > 0 && (
                      <Stack direction="row" justifyContent="space-between">
                        <Typography variant="body2" color="success.main">Discount</Typography>
                        <Typography variant="body2" color="success.main">
                          -${pricing.discountAmount?.toFixed(2)}
                        </Typography>
                      </Stack>
                    )}
                    {pricing.giftWrapCost > 0 && (
                      <Stack direction="row" justifyContent="space-between">
                        <Typography variant="body2">Gift Wrap</Typography>
                        <Typography variant="body2">${pricing.giftWrapCost?.toFixed(2)}</Typography>
                      </Stack>
                    )}
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2">Tax (8%)</Typography>
                      <Typography variant="body2">${pricing.tax?.toFixed(2)}</Typography>
                    </Stack>
                    <Divider />
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body1" fontWeight={600}>Total</Typography>
                      <Typography variant="body1" fontWeight={600} color="primary.main">
                        ${(pricing.finalPrice + pricing.tax)?.toFixed(2)}
                      </Typography>
                    </Stack>
                  </Stack>
                </Box>

                {/* Loyalty Points */}
                {pricing.loyaltyPoints > 0 && (
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'primary.50', borderRadius: 1, border: 1, borderColor: 'primary.200' }}>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <StarIcon color="primary" fontSize="small" />
                      <Typography variant="body2" color="primary.main">
                        Earn {pricing.loyaltyPoints} loyalty points with this purchase
                      </Typography>
                    </Stack>
                  </Box>
                )}
              </Box>

              {/* Variant Selector */}
              {variantTypes.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                    Product Options
                  </Typography>
                  <Stack spacing={2}>
                    {variantTypes.map((variantType) => (
                      <Box key={variantType.key}>
                        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                          {variantType.label}
                        </Typography>

                        {/* Dropdown for variants with many options */}
                        {variantType.options.length > 4 ? (
                          <FormControl fullWidth size="small">
                            <Select
                              value={variantSelections[variantType.key] || ''}
                              onChange={(e) => handleVariantChange(variantType.key, e.target.value)}
                              disabled={variantLoading}
                              sx={{ bgcolor: 'background.paper' }}
                            >
                              {variantType.options.map((option) => (
                                <MenuItem
                                  key={option.value}
                                  value={option.value}
                                  disabled={!option.available}
                                >
                                  <Stack direction="row" justifyContent="space-between" width="100%">
                                    <span>{option.label}</span>
                                    {!option.available && (
                                      <Chip label="Out of Stock" size="small" color="error" />
                                    )}
                                  </Stack>
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        ) : (
                          /* Card-based selector for fewer options */
                          <Stack direction="row" spacing={1} flexWrap="wrap">
                            {variantType.options.map((option) => (
                              <Card
                                key={option.value}
                                sx={{
                                  minWidth: 80,
                                  cursor: option.available ? 'pointer' : 'not-allowed',
                                  border: 2,
                                  borderColor: variantSelections[variantType.key] === option.value
                                    ? 'primary.main'
                                    : 'divider',
                                  bgcolor: variantSelections[variantType.key] === option.value
                                    ? 'primary.50'
                                    : option.available ? 'background.paper' : 'grey.100',
                                  transition: 'all 0.2s ease',
                                  opacity: option.available ? 1 : 0.5,
                                  '&:hover': option.available ? {
                                    borderColor: 'primary.main',
                                    transform: 'translateY(-1px)',
                                    boxShadow: 1
                                  } : {}
                                }}
                                onClick={() => option.available && handleVariantChange(variantType.key, option.value)}
                              >
                                <CardContent sx={{ p: 1.5, textAlign: 'center', '&:last-child': { pb: 1.5 } }}>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontWeight: variantSelections[variantType.key] === option.value ? 600 : 400,
                                      color: option.available ? 'text.primary' : 'text.disabled'
                                    }}
                                  >
                                    {option.label}
                                  </Typography>
                                  {!option.available && (
                                    <Typography variant="caption" color="error" sx={{ fontSize: '0.65rem' }}>
                                      Out of Stock
                                    </Typography>
                                  )}
                                </CardContent>
                              </Card>
                            ))}
                          </Stack>
                        )}
                      </Box>
                    ))}

                    {/* Selected Variant Info */}
                    {selectedVariant && (
                      <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, border: 1, borderColor: 'grey.200' }}>
                        <Stack spacing={1}>
                          <Typography variant="body2" fontWeight={600}>Selected Configuration:</Typography>
                          <Stack direction="row" spacing={2} flexWrap="wrap">
                            {Object.entries(variantSelections).map(([key, value]) => (
                              <Chip
                                key={key}
                                label={`${formatVariantLabel(key)}: ${value}`}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                            ))}
                          </Stack>
                          {selectedVariant.sku && (
                            <Typography variant="caption" color="text.secondary">
                              SKU: {selectedVariant.sku}
                            </Typography>
                          )}
                          {selectedVariant.priceUSD !== product.priceUSD && (
                            <Typography variant="body2" color="primary.main" fontWeight={600}>
                              Variant Price: ${selectedVariant.priceUSD}
                            </Typography>
                          )}
                        </Stack>
                      </Box>
                    )}
                  </Stack>
                </Box>
              )}

              {/* Quantity Selector */}
              <Box>
                <Typography variant="subtitle2" gutterBottom>Quantity</Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Box sx={{ display: 'flex', alignItems: 'center', border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <IconButton
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= (product.minOrderQty || 1)}
                      size="small"
                    >
                      <RemoveIcon />
                    </IconButton>
                    <Typography sx={{ px: 2, minWidth: 40, textAlign: 'center' }}>
                      {quantity}
                    </Typography>
                    <IconButton
                      onClick={() => setQuantity(quantity + 1)}
                      size="small"
                    >
                      <AddIcon />
                    </IconButton>
                  </Box>

                  {product.minOrderQty && (
                    <Typography variant="caption" color="text.secondary">
                      Min order: {product.minOrderQty}
                    </Typography>
                  )}
                  {product.standardPackingQty && (
                    <Typography variant="caption" color="text.secondary">
                      Standard pack: {product.standardPackingQty}
                    </Typography>
                  )}
                </Stack>
              </Box>

              {/* Gift Option & Compatibility Checker */}
              <Box>
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems={{ xs: 'stretch', sm: 'center' }}>
                  <Button
                    variant={isGift ? "contained" : "outlined"}
                    startIcon={<CardGiftcardIcon />}
                    onClick={() => setIsGift(!isGift)}
                    size="small"
                    sx={{ flex: { sm: 1 } }}
                  >
                    Send as Gift (+${product.giftWrapPrice || 5})
                  </Button>

                  <Button
                    variant="outlined"
                    startIcon={<BuildIcon />}
                    onClick={() => setShowCompatibilityChecker(!showCompatibilityChecker)}
                    size="small"
                    color="secondary"
                    sx={{ flex: { sm: 1 } }}
                  >
                    Check Compatibility
                  </Button>


                </Stack>

                {isGift && (
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    placeholder="Enter gift message (optional)"
                    value={giftMessage}
                    onChange={(e) => setGiftMessage(e.target.value)}
                    sx={{ mt: 2 }}
                    size="small"
                  />
                )}

                {/* Compatibility Checker */}
                {showCompatibilityChecker && (
                  <Paper elevation={1} sx={{ p: 2, mt: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BuildIcon color="primary" fontSize="small" />
                      Vehicle Compatibility Checker
                    </Typography>

                    <Stack spacing={2}>
                      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                        <FormControl size="small" sx={{ flex: 1 }}>
                          <InputLabel>Make</InputLabel>
                          <Select
                            value={vehicleInfo.make}
                            onChange={(e) => setVehicleInfo({...vehicleInfo, make: e.target.value})}
                          >
                            {/* Get unique makes from product compatibility data */}
                            {product.vehicleCompatibility && [...new Set(product.vehicleCompatibility.map(v => v.make))].map((make) => (
                              <MenuItem key={make} value={make}>{make}</MenuItem>
                            ))}
                          </Select>
                        </FormControl>

                        <FormControl size="small" sx={{ flex: 1 }}>
                          <InputLabel>Model</InputLabel>
                          <Select
                            value={vehicleInfo.model}
                            onChange={(e) => setVehicleInfo({...vehicleInfo, model: e.target.value})}
                            disabled={!vehicleInfo.make}
                          >
                            {/* Filter models based on selected make */}
                            {product.vehicleCompatibility &&
                              product.vehicleCompatibility
                                .filter(v => v.make === vehicleInfo.make)
                                .map((vehicle) => (
                                  <MenuItem key={vehicle.model} value={vehicle.model}>{vehicle.model}</MenuItem>
                                ))
                            }
                          </Select>
                        </FormControl>

                        <FormControl size="small" sx={{ flex: 1 }}>
                          <InputLabel>Year</InputLabel>
                          <Select
                            value={vehicleInfo.year}
                            onChange={(e) => setVehicleInfo({...vehicleInfo, year: e.target.value})}
                            disabled={!vehicleInfo.model}
                          >
                            {/* Filter years based on selected make and model */}
                            {product.vehicleCompatibility &&
                              product.vehicleCompatibility
                                .filter(v => v.make === vehicleInfo.make && v.model === vehicleInfo.model)
                                .map((vehicle) => (
                                  <MenuItem key={vehicle.year} value={vehicle.year}>{vehicle.year}</MenuItem>
                                ))
                            }
                          </Select>
                        </FormControl>
                      </Stack>

                      {/* Compatibility Result */}
                      {vehicleInfo.make && vehicleInfo.model && vehicleInfo.year && (
                        <Box>
                          {(() => {
                            const isCompatible = product.vehicleCompatibility?.some(
                              v => v.make === vehicleInfo.make &&
                                   v.model === vehicleInfo.model &&
                                   v.year.toString() === vehicleInfo.year.toString()
                            );
                            const compatibleVehicle = product.vehicleCompatibility?.find(
                              v => v.make === vehicleInfo.make &&
                                   v.model === vehicleInfo.model &&
                                   v.year.toString() === vehicleInfo.year.toString()
                            );

                            return isCompatible ? (
                              <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 1, border: 1, borderColor: 'success.200' }}>
                                <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                  <CheckCircleIcon color="success" fontSize="small" />
                                  <Typography variant="body2" color="success.main" fontWeight={600}>
                                    ✓ Compatible with your vehicle
                                  </Typography>
                                </Stack>
                                {compatibleVehicle && (
                                  <Typography variant="caption" color="text.secondary">
                                    {compatibleVehicle.engine} • {compatibleVehicle.powerHP} HP • {compatibleVehicle.fuelType}
                                  </Typography>
                                )}
                              </Box>
                            ) : (
                              <Box sx={{ p: 2, bgcolor: 'error.50', borderRadius: 1, border: 1, borderColor: 'error.200' }}>
                                <Stack direction="row" spacing={1} alignItems="center">
                                  <WarningIcon color="error" fontSize="small" />
                                  <Typography variant="body2" color="error.main" fontWeight={600}>
                                    ✗ Not compatible with your vehicle
                                  </Typography>
                                </Stack>
                                <Typography variant="caption" color="text.secondary">
                                  Please check compatible vehicles in the specifications tab
                                </Typography>
                              </Box>
                            );
                          })()}
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                )}
              </Box>

              {/* Action Buttons */}
              <Stack spacing={2}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<ShoppingCartIcon />}
                  fullWidth
                  sx={{ py: 1.5 }}
                  onClick={handleAddToCart}
                >
                  Add to Cart
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<FlashOnIcon />}
                  fullWidth
                  sx={{ py: 1.5 }}
                >
                  Buy Now
                </Button>
              </Stack>

              {/* Special Offers */}
              {product.offers && product.offers.length > 0 && (
                <Box sx={{ p: 2, bgcolor: 'warning.50', borderRadius: 1, border: 1, borderColor: 'warning.200' }}>
                  <Typography variant="subtitle2" color="warning.main" gutterBottom>
                    Special Offers
                  </Typography>
                  {product.offers.map((offer, index) => (
                    <Typography key={index} variant="body2" color="warning.dark">
                      • {offer.en}
                    </Typography>
                  ))}
                </Box>
              )}
            </Stack>
          </Paper>

          {/* Delivery & Location Services */}
          <Paper elevation={2} sx={{ p: 3, mb: 2, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LocalShippingIcon color="primary" />
              Delivery & Services
            </Typography>

            <Stack spacing={3}>
              {/* PIN Code Input */}
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Check delivery options for your location
                </Typography>
                <Stack direction="row" spacing={2}>
                  <TextField
                    placeholder="Enter 6-digit PIN code"
                    value={pinCode}
                    onChange={(e) => setPinCode(e.target.value)}
                    size="small"
                    sx={{ flexGrow: 1 }}
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <LocationOnIcon fontSize="small" />
                          </InputAdornment>
                        ),
                      }
                    }}
                  />
                  <Button
                    variant="contained"
                    onClick={handleDeliveryCheck}
                    disabled={!pinCode || pinCode.length !== 6}
                  >
                    Check
                  </Button>
                </Stack>
              </Box>

              {/* Delivery Information */}
              {deliveryInfo ? (
                <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 1, border: 1, borderColor: 'success.200' }}>
                  <Stack spacing={2}>
                    <Typography variant="subtitle2" color="success.main">
                      Delivery Available
                    </Typography>

                    <Stack spacing={1}>
                      <Stack direction="row" justifyContent="space-between">
                        <Typography variant="body2">Delivery Type</Typography>
                        <Chip label={deliveryInfo.deliveryType} size="small" color="primary" />
                      </Stack>

                      <Stack direction="row" justifyContent="space-between">
                        <Typography variant="body2">Estimated Delivery</Typography>
                        <Typography variant="body2" fontWeight={600}>
                          {deliveryInfo.deliverBy}
                        </Typography>
                      </Stack>

                      <Stack direction="row" justifyContent="space-between">
                        <Typography variant="body2">Delivery Charge</Typography>
                        <Typography variant="body2" fontWeight={600} color={deliveryInfo.deliveryCharge === 0 ? 'success.main' : 'text.primary'}>
                          {deliveryInfo.deliveryCharge === 0 ? 'FREE' : `$${deliveryInfo.deliveryCharge}`}
                        </Typography>
                      </Stack>

                      <Stack direction="row" justifyContent="space-between">
                        <Typography variant="body2">Nearest Store</Typography>
                        <Typography variant="body2">{deliveryInfo.store}</Typography>
                      </Stack>

                      {deliveryInfo.stockAtLocation > 0 && (
                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2">Store Stock</Typography>
                          <Typography variant="body2" color="success.main">
                            {deliveryInfo.stockAtLocation} available
                          </Typography>
                        </Stack>
                      )}
                    </Stack>

                    <Typography variant="caption" color="text.secondary">
                      {deliveryInfo.notes}
                    </Typography>
                  </Stack>
                </Box>
              ) : pinCode && pinCode.length === 6 ? (
                <Box sx={{ p: 2, bgcolor: 'error.50', borderRadius: 1, border: 1, borderColor: 'error.200' }}>
                  <Typography variant="body2" color="error.main">
                    Delivery not available for this PIN code
                  </Typography>
                </Box>
              ) : null}

              {/* Default Delivery Options */}
              {product.deliveryOptions && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Available Delivery Options
                  </Typography>
                  <Stack spacing={1}>
                    {product.deliveryOptions.map((option, index) => (
                      <Box
                        key={index}
                        sx={{
                          p: 2,
                          border: 1,
                          borderColor: selectedDeliveryOption === index ? 'primary.main' : 'divider',
                          borderRadius: 1,
                          cursor: 'pointer',
                          '&:hover': { borderColor: 'primary.main' }
                        }}
                        onClick={() => setSelectedDeliveryOption(index)}
                      >
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                          <Box>
                            <Typography variant="body2" fontWeight={600}>
                              {option.type}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {option.estimatedDelivery}
                            </Typography>
                          </Box>
                          <Typography variant="body2" fontWeight={600} color={option.priceUSD === 0 ? 'success.main' : 'text.primary'}>
                            {option.priceUSD === 0 ? 'FREE' : `$${option.priceUSD}`}
                          </Typography>
                        </Stack>
                      </Box>
                    ))}
                  </Stack>
                </Box>
              )}

              {/* Nearest Stores */}
              {product.nearestStores && product.nearestStores.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <StoreIcon fontSize="small" />
                    Pickup from Nearest Stores
                  </Typography>
                  <Stack spacing={1}>
                    {product.nearestStores.map((store, index) => (
                      <Box key={index} sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                        <Stack spacing={1}>
                          <Typography variant="body2" fontWeight={600}>
                            {store.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {store.address}
                          </Typography>
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography variant="caption" color={store.stock > 0 ? 'success.main' : 'error.main'}>
                              {store.stock > 0 ? `${store.stock} in stock` : 'Out of stock'}
                            </Typography>
                            <Button size="small" variant="outlined">
                              Get Directions
                            </Button>
                          </Stack>
                        </Stack>
                      </Box>
                    ))}
                  </Stack>
                </Box>
              )}
            </Stack>
          </Paper>
        </Box>
      </Box>

      {/* Tabbed Product Details Section */}
      <Paper elevation={2} sx={{ mt: 3, borderRadius: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ px: 2 }}
          >
            <Tab label="Description" />
            <Tab label="Specifications" />
            <Tab label="Compatibility" />
            <Tab label="Instructions" />
            <Tab label="Reviews" />
            <Tab label="FAQ" />
            <Tab label="Warranty" />
            <Tab label="Policies" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {/* Description Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>Product Description</Typography>
              {product.description?.en && (
                <Stack spacing={2}>
                  {product.description.en.map((desc, index) => (
                    <Typography key={index} variant="body1">
                      {desc}
                    </Typography>
                  ))}
                </Stack>
              )}

              {product.features && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>Key Features</Typography>
                  <Typography variant="body2">{product.features.en}</Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Specifications Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>Technical Specifications</Typography>

              {/* Variant-Specific Specifications */}
              {selectedVariant && Object.keys(variantSelections).length > 0 && (
                <Box sx={{ mb: 4 }}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, color: 'primary.main' }}>
                    Selected Configuration
                  </Typography>
                  <TableContainer component={Paper} elevation={1} sx={{ mb: 3 }}>
                    <Table size="small">
                      <TableBody>
                        {Object.entries(variantSelections).map(([key, value]) => (
                          <TableRow key={key}>
                            <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50', width: '30%' }}>
                              {formatVariantLabel(key)}
                            </TableCell>
                            <TableCell>{value}</TableCell>
                          </TableRow>
                        ))}
                        {selectedVariant.sku && (
                          <TableRow>
                            <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>
                              SKU
                            </TableCell>
                            <TableCell>{selectedVariant.sku}</TableCell>
                          </TableRow>
                        )}
                        {selectedVariant.priceUSD && (
                          <TableRow>
                            <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>
                              Price
                            </TableCell>
                            <TableCell>${selectedVariant.priceUSD}</TableCell>
                          </TableRow>
                        )}
                        {selectedVariant.stock !== undefined && (
                          <TableRow>
                            <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>
                              Stock
                            </TableCell>
                            <TableCell>
                              <Stack direction="row" spacing={1} alignItems="center">
                                {selectedVariant.stock > 0 ? (
                                  <>
                                    <CheckCircleIcon color="success" fontSize="small" />
                                    <span>{selectedVariant.stock} available</span>
                                  </>
                                ) : (
                                  <>
                                    <WarningIcon color="warning" fontSize="small" />
                                    <span>Out of stock</span>
                                  </>
                                )}
                              </Stack>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}

              {/* Unit of Measurement Section */}
              {product.uom && (
                <Box sx={{ mb: 4 }}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, color: 'primary.main' }}>
                    Unit of Measurement (UOM)
                  </Typography>
                  <TableContainer component={Paper} elevation={1} sx={{ mb: 3 }}>
                    <Table size="small">
                      <TableBody>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Base Unit</TableCell>
                          <TableCell>{product.uom.baseUnit}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Packing Unit</TableCell>
                          <TableCell>{product.uom.packingUnit}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Selling Unit</TableCell>
                          <TableCell>{product.uom.sellingUnit}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Measurements */}
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                    Measurements (Imperial)
                  </Typography>
                  <TableContainer component={Paper} elevation={1} sx={{ mb: 2 }}>
                    <Table size="small">
                      <TableBody>
                        {Object.entries(product.uom.measurements).map(([key, measurement]) => (
                          <TableRow key={key}>
                            <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50', textTransform: 'capitalize' }}>
                              {key}
                            </TableCell>
                            <TableCell>
                              {measurement.value} {measurement.unit}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Metric Conversions */}
                  {product.uom.conversionFactors?.metric && (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                        Measurements (Metric)
                      </Typography>
                      <TableContainer component={Paper} elevation={1} sx={{ mb: 3 }}>
                        <Table size="small">
                          <TableBody>
                            {Object.entries(product.uom.conversionFactors.metric).map(([key, measurement]) => (
                              <TableRow key={key}>
                                <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50', textTransform: 'capitalize' }}>
                                  {key}
                                </TableCell>
                                <TableCell>
                                  {measurement.value} {measurement.unit}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </>
                  )}
                </Box>
              )}

              {/* General Specifications */}
              <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, color: 'primary.main' }}>
                General Specifications
              </Typography>
              <TableContainer component={Paper} elevation={1}>
                <Table>
                  <TableBody>
                    {product.dimensions && (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Dimensions</TableCell>
                        <TableCell>{product.dimensions}</TableCell>
                      </TableRow>
                    )}
                    {product.weight && (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Weight</TableCell>
                        <TableCell>{product.weight}</TableCell>
                      </TableRow>
                    )}
                    {product.material && (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Material</TableCell>
                        <TableCell>{product.material.en}</TableCell>
                      </TableRow>
                    )}
                    {product.countryOfOrigin && (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Country of Origin</TableCell>
                        <TableCell>{product.countryOfOrigin.en}</TableCell>
                      </TableRow>
                    )}
                    {product.hazardousMaterialStatus && (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Hazardous Material</TableCell>
                        <TableCell>{product.hazardousMaterialStatus}</TableCell>
                      </TableRow>
                    )}
                    {product.category && (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Category</TableCell>
                        <TableCell>{product.category.en}</TableCell>
                      </TableRow>
                    )}
                    {product.condition && (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.50' }}>Condition</TableCell>
                        <TableCell>{product.condition.en}</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Compatibility Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>Vehicle Compatibility</Typography>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Use the compatibility checker in the purchase section above to verify if this product fits your vehicle.
              </Typography>

              {product.vehicleCompatibility && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>Compatible Vehicles</Typography>
                  <Stack spacing={1}>
                    {product.vehicleCompatibility.map((vehicle, index) => (
                      <Paper key={index} elevation={1} sx={{ p: 2 }}>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <CheckCircleIcon color="success" fontSize="small" />
                          <Box>
                            <Typography variant="body2" fontWeight={600}>
                              {vehicle.make} {vehicle.model} {vehicle.year}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {vehicle.engine} • {vehicle.powerHP} HP • {vehicle.fuelType}
                            </Typography>
                          </Box>
                        </Stack>
                      </Paper>
                    ))}
                  </Stack>
                </Box>
              )}
            </Box>
          )}

          {/* Instructions Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>Installation & Maintenance Instructions</Typography>

              {product.specialInstructions?.en && (
                <Stack spacing={3}>
                  {/* Installation Instructions */}
                  {product.specialInstructions.en.installation && (
                    <Box>
                      <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <BuildIcon color="primary" fontSize="small" />
                        Installation Instructions
                      </Typography>
                      <List>
                        {product.specialInstructions.en.installation.map((instruction, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Typography variant="body2" color="primary.main" fontWeight={600}>
                                {index + 1}.
                              </Typography>
                            </ListItemIcon>
                            <ListItemText primary={instruction} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}

                  {/* Maintenance Instructions */}
                  {product.specialInstructions.en.maintenance && (
                    <Box>
                      <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AssignmentIcon color="primary" fontSize="small" />
                        Maintenance Guidelines
                      </Typography>
                      <List>
                        {product.specialInstructions.en.maintenance.map((instruction, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Typography variant="body2" color="primary.main" fontWeight={600}>
                                {index + 1}.
                              </Typography>
                            </ListItemIcon>
                            <ListItemText primary={instruction} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}

                  {/* Safety Guidelines */}
                  {product.specialInstructions.en.safety && (
                    <Box>
                      <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <SecurityIcon color="warning" fontSize="small" />
                        Safety Guidelines
                      </Typography>
                      <List>
                        {product.specialInstructions.en.safety.map((instruction, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <WarningIcon color="warning" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText primary={instruction} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}
                </Stack>
              )}
            </Box>
          )}

          {/* Reviews Tab */}
          {activeTab === 4 && (
            <Box>
              <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                <Typography variant="h6">Customer Reviews</Typography>
                <Button
                  variant="contained"
                  startIcon={<StarIcon />}
                  onClick={() => setShowWriteReview(true)}
                  size="small"
                >
                  Write Review
                </Button>
              </Stack>

              {/* Review Statistics */}
              {reviewStats && (
                <Paper elevation={1} sx={{ p: 3, mb: 3, bgcolor: 'grey.50' }}>
                  <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} alignItems="center">
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h3" color="primary.main" fontWeight={700}>
                        {reviewStats.averageRating.toFixed(1)}
                      </Typography>
                      <Rating value={reviewStats.averageRating} readOnly precision={0.1} />
                      <Typography variant="body2" color="text.secondary">
                        {reviewStats.totalReviews} reviews
                      </Typography>
                    </Box>

                    <Box sx={{ flex: 1 }}>
                      {Object.entries(reviewStats.ratingDistribution).reverse().map(([rating, count]) => (
                        <Stack key={rating} direction="row" spacing={2} alignItems="center" sx={{ mb: 1 }}>
                          <Typography variant="body2" sx={{ minWidth: 20 }}>{rating}★</Typography>
                          <Box sx={{ flex: 1, bgcolor: 'grey.200', borderRadius: 1, height: 8 }}>
                            <Box
                              sx={{
                                width: `${(count / reviewStats.totalReviews) * 100}%`,
                                bgcolor: 'primary.main',
                                height: '100%',
                                borderRadius: 1
                              }}
                            />
                          </Box>
                          <Typography variant="body2" sx={{ minWidth: 30 }}>{count}</Typography>
                        </Stack>
                      ))}
                    </Box>
                  </Stack>
                </Paper>
              )}

              {/* Review Filters */}
              <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <InputLabel>Rating</InputLabel>
                    <Select
                      value={reviewFilters.rating}
                      onChange={(e) => setReviewFilters({...reviewFilters, rating: e.target.value})}
                    >
                      <MenuItem value="all">All Ratings</MenuItem>
                      <MenuItem value="5">5 Stars</MenuItem>
                      <MenuItem value="4">4 Stars</MenuItem>
                      <MenuItem value="3">3 Stars</MenuItem>
                      <MenuItem value="2">2 Stars</MenuItem>
                      <MenuItem value="1">1 Star</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <InputLabel>Verified</InputLabel>
                    <Select
                      value={reviewFilters.verified}
                      onChange={(e) => setReviewFilters({...reviewFilters, verified: e.target.value})}
                    >
                      <MenuItem value="all">All Reviews</MenuItem>
                      <MenuItem value="verified">Verified Only</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <InputLabel>Sort By</InputLabel>
                    <Select
                      value={reviewFilters.sortBy}
                      onChange={(e) => setReviewFilters({...reviewFilters, sortBy: e.target.value})}
                    >
                      <MenuItem value="newest">Newest</MenuItem>
                      <MenuItem value="oldest">Oldest</MenuItem>
                      <MenuItem value="highest_rated">Highest Rated</MenuItem>
                      <MenuItem value="lowest_rated">Lowest Rated</MenuItem>
                      <MenuItem value="most_helpful">Most Helpful</MenuItem>
                    </Select>
                  </FormControl>
                </Stack>
              </Paper>

              {/* Reviews List */}
              {reviews && Array.isArray(reviews) && reviews.length > 0 ? (
                <Stack spacing={3}>
                  {reviews.map((review) => (
                    <Paper key={review.id} elevation={1} sx={{ p: 3 }}>
                      <Stack spacing={2}>
                        {/* Review Header */}
                        <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                          <Stack direction="row" spacing={2} alignItems="center">
                            <Avatar src={review.reviewer.avatar} sx={{ bgcolor: 'primary.main' }}>
                              {review.reviewer.name.charAt(0)}
                            </Avatar>
                            <Box>
                              <Stack direction="row" spacing={1} alignItems="center">
                                <Typography variant="subtitle2">{review.reviewer.name}</Typography>
                                {review.verified && (
                                  <Chip label="Verified Purchase" size="small" color="success" variant="outlined" />
                                )}
                              </Stack>
                              <Typography variant="caption" color="text.secondary">
                                {review.date}
                              </Typography>
                            </Box>
                          </Stack>
                          <Rating value={review.rating} readOnly size="small" />
                        </Stack>

                        {/* Review Title */}
                        {review.title && (
                          <Typography variant="subtitle1" fontWeight={600}>
                            {review.title}
                          </Typography>
                        )}

                        {/* Review Content */}
                        <Typography variant="body2">{review.comment}</Typography>

                        {/* Pros and Cons */}
                        {(review.pros?.length > 0 || review.cons?.length > 0) && (
                          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2}>
                            {review.pros?.length > 0 && (
                              <Box sx={{ flex: 1 }}>
                                <Typography variant="subtitle2" color="success.main" gutterBottom>
                                  👍 Pros
                                </Typography>
                                <List dense>
                                  {review.pros.map((pro, index) => (
                                    <ListItem key={index} sx={{ py: 0 }}>
                                      <ListItemText primary={pro} />
                                    </ListItem>
                                  ))}
                                </List>
                              </Box>
                            )}
                            {review.cons?.length > 0 && (
                              <Box sx={{ flex: 1 }}>
                                <Typography variant="subtitle2" color="error.main" gutterBottom>
                                  👎 Cons
                                </Typography>
                                <List dense>
                                  {review.cons.map((con, index) => (
                                    <ListItem key={index} sx={{ py: 0 }}>
                                      <ListItemText primary={con} />
                                    </ListItem>
                                  ))}
                                </List>
                              </Box>
                            )}
                          </Stack>
                        )}

                        {/* Attachments */}
                        {review.attachments?.length > 0 && (
                          <Box>
                            <Typography variant="subtitle2" gutterBottom>Attachments</Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap">
                              {review.attachments.map((attachment, index) => (
                                <Chip
                                  key={index}
                                  label={attachment.caption || `${attachment.type} attachment`}
                                  size="small"
                                  variant="outlined"
                                  clickable
                                />
                              ))}
                            </Stack>
                          </Box>
                        )}

                        {/* Review Actions */}
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Button size="small" startIcon={<ThumbUpIcon />}>
                            Helpful ({review.helpful})
                          </Button>
                          <Button size="small" startIcon={<ReportIcon />}>
                            Report
                          </Button>
                          <Button size="small" startIcon={<ShareIcon />}>
                            Share
                          </Button>
                          {review.wouldRecommend && (
                            <Chip label="Recommends" size="small" color="success" variant="outlined" />
                          )}
                        </Stack>

                        {/* Replies */}
                        {review.replies?.length > 0 && (
                          <Box sx={{ ml: 4, mt: 2 }}>
                            {review.replies.map((reply) => (
                              <Paper key={reply.id} elevation={0} sx={{ p: 2, bgcolor: 'grey.50' }}>
                                <Stack spacing={1}>
                                  <Stack direction="row" spacing={1} alignItems="center">
                                    <Typography variant="subtitle2" color="primary.main">
                                      {reply.author}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {reply.date}
                                    </Typography>
                                  </Stack>
                                  <Typography variant="body2">{reply.comment}</Typography>
                                </Stack>
                              </Paper>
                            ))}
                          </Box>
                        )}
                      </Stack>
                    </Paper>
                  ))}
                </Stack>
              ) : (
                <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No reviews yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Be the first to review this product and help other customers!
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<StarIcon />}
                    onClick={() => setShowWriteReview(true)}
                  >
                    Write First Review
                  </Button>
                </Paper>
              )}
            </Box>
          )}

          {/* FAQ Tab */}
          {activeTab === 5 && (
            <Box>
              <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                <Typography variant="h6">Frequently Asked Questions</Typography>
                <Button
                  variant="outlined"
                  startIcon={<QuestionAnswerIcon />}
                  onClick={() => setShowAskQuestion(true)}
                  size="small"
                >
                  Ask a Question
                </Button>
              </Stack>

              {faqList && Array.isArray(faqList) && faqList.length > 0 ? (
                <Stack spacing={2}>
                  {faqList.map((faq) => (
                    <Accordion key={faq.id}>
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        sx={{
                          '&:hover': { bgcolor: 'grey.50' },
                          borderRadius: 1
                        }}
                      >
                        <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {faq.question}
                            </Typography>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 0.5 }}>
                              <Chip label={faq.category} size="small" variant="outlined" />
                              {faq.verified && (
                                <Chip label="Verified" size="small" color="success" variant="outlined" />
                              )}
                              <Typography variant="caption" color="text.secondary">
                                Asked by {faq.askedBy}
                              </Typography>
                            </Stack>
                          </Box>
                          <Stack direction="row" spacing={1} alignItems="center">
                            <ThumbUpIcon fontSize="small" color="action" />
                            <Typography variant="caption">{faq.helpful}</Typography>
                          </Stack>
                        </Stack>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Stack spacing={2}>
                          <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                            {faq.answer}
                          </Typography>

                          {faq.attachments?.length > 0 && (
                            <Box>
                              <Typography variant="subtitle2" gutterBottom>Attachments</Typography>
                              <Stack direction="row" spacing={1} flexWrap="wrap">
                                {faq.attachments.map((attachment, index) => (
                                  <Chip
                                    key={index}
                                    label={attachment.caption || `${attachment.type} attachment`}
                                    size="small"
                                    variant="outlined"
                                    clickable
                                  />
                                ))}
                              </Stack>
                            </Box>
                          )}

                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Stack direction="row" spacing={2}>
                              <Button size="small" startIcon={<ThumbUpIcon />}>
                                Helpful ({faq.helpful})
                              </Button>
                              <Button size="small" startIcon={<ShareIcon />}>
                                Share
                              </Button>
                            </Stack>
                            <Typography variant="caption" color="text.secondary">
                              Answered by {faq.answeredBy} on {faq.dateAnswered}
                            </Typography>
                          </Stack>
                        </Stack>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </Stack>
              ) : (
                <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No questions yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Be the first to ask a question about this product!
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<QuestionAnswerIcon />}
                    onClick={() => setShowAskQuestion(true)}
                  >
                    Ask First Question
                  </Button>
                </Paper>
              )}
            </Box>
          )}

          {/* Warranty Tab */}
          {activeTab === 6 && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SecurityIcon color="primary" />
                Warranty Information
              </Typography>

              {product.warranty && (
                <Stack spacing={3}>
                  <Box sx={{ p: 3, bgcolor: 'success.50', borderRadius: 2, border: 1, borderColor: 'success.200' }}>
                    <Stack direction="row" spacing={2} alignItems="center">
                      <VerifiedIcon color="success" />
                      <Box>
                        <Typography variant="subtitle1" color="success.main">
                          {product.warranty.en} Warranty
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Valid from delivery date
                        </Typography>
                      </Box>
                    </Stack>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" gutterBottom>Terms & Conditions</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {product.warranty.termsAndConditions}
                    </Typography>
                  </Box>

                  {product.warranty.certificates && (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>Warranty Documents</Typography>
                      <Stack spacing={1}>
                        {product.warranty.certificates.map((cert, index) => (
                          <Button
                            key={index}
                            variant="outlined"
                            size="small"
                            startIcon={<AssignmentIcon />}
                            sx={{ justifyContent: 'flex-start' }}
                          >
                            {cert.name}
                          </Button>
                        ))}
                      </Stack>
                    </Box>
                  )}
                </Stack>
              )}
            </Box>
          )}

          {/* Policies Tab */}
          {activeTab === 7 && (
            <Box>
              <Typography variant="h6" gutterBottom>Return & Exchange Policies</Typography>

              <Stack spacing={3}>
                {/* Return Policy */}
                {product.returnPolicy && (
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>Return Policy</Typography>
                    <Paper elevation={1} sx={{ p: 3, bgcolor: 'grey.50' }}>
                      <Stack spacing={2}>
                        <Typography variant="body2">
                          <strong>Return Period:</strong> {product.returnPolicy.en}
                        </Typography>

                        <Box>
                          <Typography variant="body2" gutterBottom><strong>Return Reasons:</strong></Typography>
                          <List dense>
                            {product.returnPolicy.reasonsAssociation?.map((reason, index) => (
                              <ListItem key={index}>
                                <ListItemText primary={reason} />
                              </ListItem>
                            ))}
                          </List>
                        </Box>

                        <Typography variant="body2">
                          <strong>Terms:</strong> {product.returnPolicy.termsAndConditions}
                        </Typography>
                      </Stack>
                    </Paper>
                  </Box>
                )}

                {/* Exchange Policy */}
                {product.returnPolicy?.exchangeable && (
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>Exchange Policy</Typography>
                    <Paper elevation={1} sx={{ p: 3, bgcolor: 'grey.50' }}>
                      <Stack spacing={2}>
                        <Typography variant="body2">
                          <strong>Exchange Period:</strong> {product.returnPolicy.exchangeableDaysFromDelivery} days from delivery
                        </Typography>

                        <Box>
                          <Typography variant="body2" gutterBottom><strong>Exchange Reasons:</strong></Typography>
                          <List dense>
                            {product.returnPolicy.exchangeReasonsAssociation?.map((reason, index) => (
                              <ListItem key={index}>
                                <ListItemText primary={reason} />
                              </ListItem>
                            ))}
                          </List>
                        </Box>

                        <Typography variant="body2">
                          <strong>Terms:</strong> {product.returnPolicy.exchangeTermsAndConditions}
                        </Typography>
                      </Stack>
                    </Paper>
                  </Box>
                )}
              </Stack>
            </Box>
          )}
        </Box>
      </Paper>

      {/* Bundle/Kit Offers */}
      {productBundles.length > 0 && (
        <Paper elevation={2} sx={{ mt: 3, p: 3, borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom>Bundle Offers</Typography>
          <Stack spacing={2}>
            {productBundles.map((bundle, bundleIndex) => (
              <Box key={bundleIndex}>
                {bundle.kits.map((kit, kitIndex) => (
                  <Paper key={kitIndex} elevation={1} sx={{ p: 3, mb: 2, bgcolor: 'warning.50', border: 1, borderColor: 'warning.200' }}>
                    <Stack spacing={2}>
                      <Typography variant="subtitle1" fontWeight={600}>
                        {kit.bundleName}
                      </Typography>

                      <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
                        {kit.products.map((bundleProduct, productIndex) => (
                          <Box key={productIndex} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Card
                              sx={{
                                width: 120,
                                height: 120,
                                cursor: 'pointer',
                                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                '&:hover': {
                                  transform: 'scale(1.08)',
                                  boxShadow: 2
                                },
                                borderRadius: 1,
                                border: '1px solid',
                                borderColor: 'grey.200'
                              }}
                              onClick={() => navigate(`/product/${bundleProduct.id || bundleProduct.productId}`)}
                            >
                              <CardMedia
                                component="img"
                                height="120"
                                image={bundleProduct.images?.[0] || bundleProduct.image || bundleProduct.imageUrl || '/src/assets/images/placeholder.jpg'}
                                alt={bundleProduct.name}
                                sx={{
                                  objectFit: 'contain',
                                  bgcolor: 'grey.50',
                                  width: '100%',
                                  height: '120px',
                                  minHeight: '120px',
                                  maxHeight: '120px',
                                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                  '&:hover': {
                                    filter: 'brightness(1.1)'
                                  },
                                  borderRadius: 1,
                                  imageRendering: 'crisp-edges'
                                }}
                                onError={(e) => {
                                  e.target.src = '/src/assets/images/placeholder.jpg';
                                }}
                              />
                            </Card>
                            <Typography variant="body2" sx={{ ml: 1 }}>{bundleProduct.name}</Typography>
                            {productIndex < kit.products.length - 1 && (
                              <Typography variant="body2" color="text.secondary">+</Typography>
                            )}
                          </Box>
                        ))}
                      </Stack>

                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Typography variant="h6" color="primary.main" fontWeight={700}>
                          ${kit.offerPriceUSD}
                        </Typography>
                        <Button variant="contained" size="small">
                          Add Bundle to Cart
                        </Button>
                      </Stack>
                    </Stack>
                  </Paper>
                ))}
              </Box>
            ))}
          </Stack>
        </Paper>
      )}

      {/* Related Products Section */}
      <Stack spacing={3} sx={{ mt: 3 }}>
        {/* Must-have Products */}
        {product.relatedProducts && product.relatedProducts.length > 0 && (
          <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>Must-Have Products</Typography>
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 2,
              justifyContent: { xs: 'center', sm: 'flex-start' }
            }}>
              {product.relatedProducts.map((relatedProduct) => (
                <Card
                  key={relatedProduct.id}
                  sx={{
                    width: { xs: 140, sm: 160, md: 180 },
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 25px rgba(46, 192, 203, 0.15)'
                    }
                  }}
                  onClick={() => navigate(`/product/${relatedProduct.id}`)}
                >
                  <Box sx={{ position: 'relative', overflow: 'hidden', height: 100 }}>
                    <CardMedia
                      component="img"
                      height="100"
                      image={relatedProduct.images?.[0] || relatedProduct.imageUrl || '/src/assets/images/placeholder.jpg'}
                      alt={relatedProduct.name.en}
                      sx={{
                        objectFit: 'contain',
                        bgcolor: 'grey.50',
                        width: '100%',
                        height: '100px',
                        minHeight: '100px',
                        maxHeight: '100px',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                          transform: 'scale(1.08)',
                          filter: 'brightness(1.1)'
                        },
                        border: '1px solid',
                        borderColor: 'grey.200',
                        borderRadius: 1,
                        imageRendering: 'crisp-edges'
                      }}
                      onError={(e) => {
                        e.target.src = '/src/assets/images/placeholder.jpg';
                      }}
                    />
                  </Box>
                  <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                    <Typography
                      variant="caption"
                      sx={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        lineHeight: 1.2,
                        height: '2.4em',
                        fontSize: '0.75rem'
                      }}
                    >
                      {relatedProduct.name.en}
                    </Typography>
                    <Typography
                      variant="subtitle2"
                      color="primary.main"
                      fontWeight={600}
                      sx={{ mt: 0.5, fontSize: '0.875rem' }}
                    >
                      ${relatedProduct.priceUSD}
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      fullWidth
                      sx={{
                        mt: 1,
                        py: 0.5,
                        fontSize: '0.7rem',
                        borderRadius: 1,
                        textTransform: 'none'
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/product/${relatedProduct.id}`);
                      }}
                    >
                      View
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </Box>
          </Paper>
        )}

        {/* More from Brand */}
        {product.moreFromBrand && product.moreFromBrand.length > 0 && (
          <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>More from {product.brand?.en}</Typography>
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 2,
              justifyContent: { xs: 'center', sm: 'flex-start' }
            }}>
              {product.moreFromBrand.map((brandProduct) => (
                <Card
                  key={brandProduct.id}
                  sx={{
                    width: { xs: 140, sm: 160, md: 180 },
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 25px rgba(46, 192, 203, 0.15)'
                    }
                  }}
                  onClick={() => navigate(`/product/${brandProduct.id}`)}
                >
                  <Box sx={{ position: 'relative', overflow: 'hidden', height: 100 }}>
                    <CardMedia
                      component="img"
                      height="100"
                      image={brandProduct.images?.[0] || brandProduct.imageUrl || '/src/assets/images/placeholder.jpg'}
                      alt={brandProduct.name.en}
                      sx={{
                        objectFit: 'contain',
                        bgcolor: 'grey.50',
                        width: '100%',
                        height: '100px',
                        minHeight: '100px',
                        maxHeight: '100px',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                          transform: 'scale(1.08)',
                          filter: 'brightness(1.1)'
                        },
                        border: '1px solid',
                        borderColor: 'grey.200',
                        borderRadius: 1,
                        imageRendering: 'crisp-edges'
                      }}
                      onError={(e) => {
                        e.target.src = '/src/assets/images/placeholder.jpg';
                      }}
                    />
                  </Box>
                  <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                    <Typography
                      variant="caption"
                      sx={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        lineHeight: 1.2,
                        height: '2.4em',
                        fontSize: '0.75rem'
                      }}
                    >
                      {brandProduct.name.en}
                    </Typography>
                    <Typography
                      variant="subtitle2"
                      color="primary.main"
                      fontWeight={600}
                      sx={{ mt: 0.5, fontSize: '0.875rem' }}
                    >
                      ${brandProduct.priceUSD}
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      fullWidth
                      sx={{
                        mt: 1,
                        py: 0.5,
                        fontSize: '0.7rem',
                        borderRadius: 1,
                        textTransform: 'none'
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/product/${brandProduct.id}`);
                      }}
                    >
                      View
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </Box>
          </Paper>
        )}

        {/* Similar Products for Comparison */}
        {similarProducts[product.id] && (
          <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CompareArrowsIcon color="primary" />
              Compare Similar Products
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {similarProducts[product.id].reason}
            </Typography>

            <Stack spacing={3}>
              {/* Similar Products */}
              {similarProducts[product.id].similar && similarProducts[product.id].similar.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>Similar Products</Typography>
                  <Stack direction="row" spacing={2} sx={{ overflowX: 'auto', pb: 1 }}>
                    {similarProducts[product.id].similar.slice(0, 4).map((productId) => {
                      const similarProduct = masterProductList.find(p => p.id === productId);
                      if (!similarProduct) return null;

                      return (
                        <Card
                          key={productId}
                          sx={{
                            minWidth: 200,
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: '0 8px 25px rgba(46, 192, 203, 0.15)'
                            }
                          }}
                          onClick={(e) => {
                            if (e.ctrlKey || e.metaKey) {
                              // Open in new tab if Ctrl/Cmd is held
                              window.open(`/product/${productId}`, '_blank');
                            } else {
                              // Navigate to product page
                              navigate(`/product/${productId}`);
                            }
                          }}
                        >
                          <CardMedia
                            component="img"
                            height="120"
                            image={similarProduct.images?.[0] || similarProduct.imageUrl || '/src/assets/images/placeholder.jpg'}
                            alt={similarProduct.name?.en}
                            sx={{
                              objectFit: 'contain',
                              bgcolor: 'grey.50',
                              width: '100%',
                              height: '120px',
                              minHeight: '120px',
                              maxHeight: '120px',
                              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                              '&:hover': {
                                transform: 'scale(1.08)',
                                filter: 'brightness(1.1)'
                              },
                              border: '1px solid',
                              borderColor: 'grey.200',
                              borderRadius: 1,
                              imageRendering: 'crisp-edges'
                            }}
                            onError={(e) => {
                              e.target.src = '/src/assets/images/placeholder.jpg';
                            }}
                          />
                          <CardContent sx={{ p: 2 }}>
                            <Typography variant="body2" sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              mb: 1
                            }}>
                              {similarProduct.name?.en}
                            </Typography>
                            <Typography variant="h6" color="primary.main">
                              ${similarProduct.priceUSD}
                            </Typography>
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<CompareArrowsIcon />}
                              sx={{ mt: 1 }}
                              fullWidth
                              onClick={(e) => {
                                e.stopPropagation();
                                setComparisonProducts([product.id, productId]);
                                setShowComparison(true);
                              }}
                            >
                              Compare
                            </Button>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </Stack>
                </Box>
              )}

              {/* Other Brands */}
              {similarProducts[product.id].otherBrands && similarProducts[product.id].otherBrands.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>Other Brands</Typography>
                  <Stack direction="row" spacing={2} sx={{ overflowX: 'auto', pb: 1 }}>
                    {similarProducts[product.id].otherBrands.slice(0, 4).map((productId) => {
                      const otherBrandProduct = masterProductList.find(p => p.id === productId);
                      if (!otherBrandProduct) return null;

                      return (
                        <Card
                          key={productId}
                          sx={{
                            minWidth: 200,
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: '0 8px 25px rgba(46, 192, 203, 0.15)'
                            }
                          }}
                          onClick={(e) => {
                            if (e.ctrlKey || e.metaKey) {
                              // Open in new tab if Ctrl/Cmd is held
                              window.open(`/product/${productId}`, '_blank');
                            } else {
                              // Navigate to product page
                              navigate(`/product/${productId}`);
                            }
                          }}
                        >
                          <CardMedia
                            component="img"
                            height="120"
                            image={otherBrandProduct.images?.[0] || otherBrandProduct.imageUrl || '/src/assets/images/placeholder.jpg'}
                            alt={otherBrandProduct.name?.en}
                            sx={{
                              objectFit: 'contain',
                              bgcolor: 'grey.50',
                              width: '100%',
                              height: '120px',
                              minHeight: '120px',
                              maxHeight: '120px',
                              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                              '&:hover': {
                                transform: 'scale(1.08)',
                                filter: 'brightness(1.1)'
                              },
                              border: '1px solid',
                              borderColor: 'grey.200',
                              borderRadius: 1,
                              imageRendering: 'crisp-edges'
                            }}
                            onError={(e) => {
                              e.target.src = '/src/assets/images/placeholder.jpg';
                            }}
                          />
                          <CardContent sx={{ p: 2 }}>
                            <Typography variant="body2" sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              mb: 1
                            }}>
                              {otherBrandProduct.name?.en}
                            </Typography>
                            <Typography variant="h6" color="primary.main">
                              ${otherBrandProduct.priceUSD}
                            </Typography>
                            <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                              <Chip
                                label={otherBrandProduct.brand?.en}
                                size="small"
                                variant="outlined"
                              />
                              <Button
                                variant="outlined"
                                size="small"
                                startIcon={<CompareArrowsIcon />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setComparisonProducts([product.id, productId]);
                                  setShowComparison(true);
                                }}
                              >
                                Compare
                              </Button>
                            </Stack>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </Stack>
                </Box>
              )}

              {/* Compare All Button */}
              <Box sx={{ textAlign: 'center' }}>
                <Button
                  variant="contained"
                  startIcon={<CompareArrowsIcon />}
                  onClick={() => {
                    const allProducts = [
                      product.id,
                      ...(similarProducts[product.id].similar || []).slice(0, 2),
                      ...(similarProducts[product.id].otherBrands || []).slice(0, 1)
                    ];
                    setComparisonProducts(allProducts);
                    setShowComparison(true);
                  }}
                  size="large"
                >
                  Compare All Similar Products
                </Button>
              </Box>
            </Stack>
          </Paper>
        )}

        {/* Additional Information */}
        <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <BusinessIcon color="primary" />
            Additional Information
          </Typography>

          <Stack spacing={2}>
            {/* Brand Information */}
            {product.brand && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>Brand Information</Typography>
                <Typography variant="body2" color="text.secondary">
                  {product.brand.en} - Trusted automotive parts manufacturer
                </Typography>
              </Box>
            )}

            {/* Supplier Details */}
            {product.soldBy && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>Supplier Details</Typography>
                <Typography variant="body2" color="text.secondary">
                  Sold by: {product.soldBy}
                </Typography>
                {product.supplierContact && (
                  <Typography variant="body2" color="text.secondary">
                    Contact: {product.supplierContact}
                  </Typography>
                )}
              </Box>
            )}

            {/* Product Identifiers */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>Product Identifiers</Typography>
              <Stack spacing={1}>
                {product.upc && (
                  <Typography variant="body2" color="text.secondary">
                    <strong>UPC:</strong> {product.upc}
                  </Typography>
                )}
                {product.ean && (
                  <Typography variant="body2" color="text.secondary">
                    <strong>EAN:</strong> {product.ean}
                  </Typography>
                )}
                {product.rfid && (
                  <Typography variant="body2" color="text.secondary">
                    <strong>RFID:</strong> {product.rfid.enabled ? 'Enabled' : 'Not Available'}
                    {product.rfid.enabled && product.rfid.frequency && (
                      <span> ({product.rfid.frequency})</span>
                    )}
                  </Typography>
                )}
                {product.batchLotNumber && (
                  <Typography variant="body2" color="text.secondary">
                    <strong>Batch/Lot Number:</strong> {product.batchLotNumber}
                  </Typography>
                )}
              </Stack>
            </Box>

            {/* Certifications */}
            {(product.regulatoryComplianceTags || product.certifications) && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>Certifications & Compliance</Typography>
                <Stack spacing={1}>
                  {product.regulatoryComplianceTags && (
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        <strong>Regulatory Compliance:</strong>
                      </Typography>
                      <Stack direction="row" spacing={1} flexWrap="wrap">
                        {product.regulatoryComplianceTags.map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag}
                            size="small"
                            variant="outlined"
                            color="primary"
                            sx={{ fontSize: '0.75rem' }}
                          />
                        ))}
                      </Stack>
                    </Box>
                  )}
                  {product.certifications && (
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        <strong>Quality Certifications:</strong>
                      </Typography>
                      <Stack direction="row" spacing={1} flexWrap="wrap">
                        {product.certifications.map((cert, index) => (
                          <Chip
                            key={index}
                            label={cert}
                            size="small"
                            variant="filled"
                            color="success"
                            sx={{ fontSize: '0.75rem' }}
                          />
                        ))}
                      </Stack>
                    </Box>
                  )}
                </Stack>
              </Box>
            )}

            {/* Additional Details */}
            {product.additionalDetails && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>Product Analytics</Typography>
                <Stack spacing={1}>
                  <Typography variant="body2" color="text.secondary">
                    Return Rate: {product.additionalDetails.returnRate}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Movement Type: {product.additionalDetails.movementType}
                  </Typography>
                  {product.additionalDetails.rawMaterialsUsed && (
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Raw Materials:
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {product.additionalDetails.rawMaterialsUsed.join(', ')}
                      </Typography>
                    </Box>
                  )}
                </Stack>
              </Box>
            )}
          </Stack>
        </Paper>
      </Stack>

      {/* Image Dialog */}
      <Dialog
        open={showImageDialog}
        onClose={() => setShowImageDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogContent sx={{ p: 0 }}>
          <img
            src={product.images?.[selectedImage] || product.imageUrl || '/src/assets/images/placeholder.jpg'}
            alt={product.name.en}
            style={{ width: '100%', height: 'auto' }}
            onError={(e) => {
              e.target.src = '/src/assets/images/placeholder.jpg';
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowImageDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Write Review Dialog */}
      <Dialog
        open={showWriteReview}
        onClose={() => setShowWriteReview(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Write a Review</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <Box>
              <Typography variant="subtitle2" gutterBottom>Overall Rating</Typography>
              <Rating
                value={newReview.rating}
                onChange={(_, value) => setNewReview({...newReview, rating: value})}
                size="large"
              />
            </Box>

            <TextField
              label="Review Title"
              value={newReview.title}
              onChange={(e) => setNewReview({...newReview, title: e.target.value})}
              fullWidth
              placeholder="Summarize your experience"
            />

            <TextField
              label="Your Review"
              value={newReview.comment}
              onChange={(e) => setNewReview({...newReview, comment: e.target.value})}
              multiline
              rows={4}
              fullWidth
              placeholder="Share your thoughts about this product..."
            />

            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <TextField
                label="Pros (optional)"
                value={newReview.pros}
                onChange={(e) => setNewReview({...newReview, pros: e.target.value})}
                multiline
                rows={2}
                fullWidth
                placeholder="What did you like?"
              />
              <TextField
                label="Cons (optional)"
                value={newReview.cons}
                onChange={(e) => setNewReview({...newReview, cons: e.target.value})}
                multiline
                rows={2}
                fullWidth
                placeholder="What could be improved?"
              />
            </Stack>

            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Would you recommend this product?</Typography>
              <Button
                variant={newReview.wouldRecommend ? "contained" : "outlined"}
                onClick={() => setNewReview({...newReview, wouldRecommend: true})}
                size="small"
              >
                Yes
              </Button>
              <Button
                variant={!newReview.wouldRecommend ? "contained" : "outlined"}
                onClick={() => setNewReview({...newReview, wouldRecommend: false})}
                size="small"
              >
                No
              </Button>
            </Stack>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowWriteReview(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={() => {
              setShowWriteReview(false);
              setSnackbarMessage('Review submitted successfully!');
              setShowSnackbar(true);
            }}
          >
            Submit Review
          </Button>
        </DialogActions>
      </Dialog>

      {/* Ask Question Dialog */}
      <Dialog
        open={showAskQuestion}
        onClose={() => setShowAskQuestion(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Ask a Question</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={newQuestion.category}
                onChange={(e) => setNewQuestion({...newQuestion, category: e.target.value})}
              >
                <MenuItem value="General">General</MenuItem>
                <MenuItem value="Compatibility">Compatibility</MenuItem>
                <MenuItem value="Installation">Installation</MenuItem>
                <MenuItem value="Performance">Performance</MenuItem>
                <MenuItem value="Maintenance">Maintenance</MenuItem>
                <MenuItem value="Specifications">Specifications</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Your Question"
              value={newQuestion.question}
              onChange={(e) => setNewQuestion({...newQuestion, question: e.target.value})}
              multiline
              rows={4}
              fullWidth
              placeholder="What would you like to know about this product?"
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAskQuestion(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={() => {
              setShowAskQuestion(false);
              setSnackbarMessage('Question submitted successfully!');
              setShowSnackbar(true);
            }}
          >
            Submit Question
          </Button>
        </DialogActions>
      </Dialog>

      {/* Product Comparison Dialog */}
      <ProductComparison
        open={showComparison}
        onClose={() => setShowComparison(false)}
        initialProducts={comparisonProducts}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={showSnackbar}
        autoHideDuration={3000}
        onClose={() => setShowSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setShowSnackbar(false)} severity="success">
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
}

export default ProductDetailPage;