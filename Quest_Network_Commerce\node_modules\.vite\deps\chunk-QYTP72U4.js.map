{"version": 3, "sources": ["../../@mui/utils/esm/isHostComponent/isHostComponent.js", "../../@mui/utils/esm/appendOwnerState/appendOwnerState.js", "../../@mui/utils/esm/resolveComponentProps/resolveComponentProps.js", "../../@mui/utils/esm/extractEventHandlers/extractEventHandlers.js", "../../@mui/utils/esm/omitEventHandlers/omitEventHandlers.js", "../../@mui/utils/esm/mergeSlotProps/mergeSlotProps.js", "../../@mui/material/esm/utils/useSlot.js"], "sourcesContent": ["/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nfunction isHostComponent(element) {\n  return typeof element === 'string';\n}\nexport default isHostComponent;", "import isHostComponent from \"../isHostComponent/index.js\";\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nfunction appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return {\n    ...otherProps,\n    ownerState: {\n      ...otherProps.ownerState,\n      ...ownerState\n    }\n  };\n}\nexport default appendOwnerState;", "/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nfunction resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}\nexport default resolveComponentProps;", "/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nfunction extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default extractEventHandlers;", "/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nfunction omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default omitEventHandlers;", "import clsx from 'clsx';\nimport extractEventHandlers from \"../extractEventHandlers/index.js\";\nimport omitEventHandlers from \"../omitEventHandlers/index.js\";\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;", "'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n    className,\n    elementType: initialElementType,\n    ownerState,\n    externalForwardedProps,\n    internalForwardedProps,\n    shouldForwardComponentProp = false,\n    ...useSlotPropsParams\n  } = parameters;\n  const {\n    component: rootComponent,\n    slots = {\n      [name]: undefined\n    },\n    slotProps = {\n      [name]: undefined\n    },\n    ...other\n  } = externalForwardedProps;\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const {\n    props: {\n      component: slotComponent,\n      ...mergedProps\n    },\n    internalRef\n  } = mergeSlotProps({\n    className,\n    ...useSlotPropsParams,\n    externalForwardedProps: name === 'root' ? other : undefined,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.ref);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, {\n    ...(name === 'root' && !rootComponent && !slots[name] && internalForwardedProps),\n    ...(name !== 'root' && !slots[name] && internalForwardedProps),\n    ...mergedProps,\n    ...(LeafComponent && !shouldForwardComponentProp && {\n      as: LeafComponent\n    }),\n    ...(LeafComponent && shouldForwardComponentProp && {\n      component: LeafComponent\n    }),\n    ref\n  }, ownerState);\n  return [elementType, props];\n}"], "mappings": ";;;;;;;;AAGA,SAAS,gBAAgB,SAAS;AAChC,SAAO,OAAO,YAAY;AAC5B;AACA,IAAO,0BAAQ;;;ACSf,SAAS,iBAAiB,aAAa,YAAY,YAAY;AAC7D,MAAI,gBAAgB,UAAa,wBAAgB,WAAW,GAAG;AAC7D,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,YAAY;AAAA,MACV,GAAG,WAAW;AAAA,MACd,GAAG;AAAA,IACL;AAAA,EACF;AACF;AACA,IAAO,2BAAQ;;;ACvBf,SAAS,sBAAsB,gBAAgB,YAAY,WAAW;AACpE,MAAI,OAAO,mBAAmB,YAAY;AACxC,WAAO,eAAe,YAAY,SAAS;AAAA,EAC7C;AACA,SAAO;AACT;AACA,IAAO,gCAAQ;;;ACHf,SAAS,qBAAqB,QAAQ,cAAc,CAAC,GAAG;AACtD,MAAI,WAAW,QAAW;AACxB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,MAAM,EAAE,OAAO,UAAQ,KAAK,MAAM,UAAU,KAAK,OAAO,OAAO,IAAI,MAAM,cAAc,CAAC,YAAY,SAAS,IAAI,CAAC,EAAE,QAAQ,UAAQ;AAC9I,WAAO,IAAI,IAAI,OAAO,IAAI;AAAA,EAC5B,CAAC;AACD,SAAO;AACT;AACA,IAAO,+BAAQ;;;ACVf,SAAS,kBAAkB,QAAQ;AACjC,MAAI,WAAW,QAAW;AACxB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,MAAM,EAAE,OAAO,UAAQ,EAAE,KAAK,MAAM,UAAU,KAAK,OAAO,OAAO,IAAI,MAAM,WAAW,EAAE,QAAQ,UAAQ;AAClH,WAAO,IAAI,IAAI,OAAO,IAAI;AAAA,EAC5B,CAAC;AACD,SAAO;AACT;AACA,IAAO,4BAAQ;;;ACDf,SAAS,eAAe,YAAY;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,cAAc;AAGjB,UAAMA,iBAAgB,aAAK,mDAAiB,WAAW,WAAW,iEAAwB,WAAW,uDAAmB,SAAS;AACjI,UAAMC,eAAc;AAAA,MAClB,GAAG,mDAAiB;AAAA,MACpB,GAAG,iEAAwB;AAAA,MAC3B,GAAG,uDAAmB;AAAA,IACxB;AACA,UAAMC,SAAQ;AAAA,MACZ,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,QAAIF,eAAc,SAAS,GAAG;AAC5B,MAAAE,OAAM,YAAYF;AAAA,IACpB;AACA,QAAI,OAAO,KAAKC,YAAW,EAAE,SAAS,GAAG;AACvC,MAAAC,OAAM,QAAQD;AAAA,IAChB;AACA,WAAO;AAAA,MACL,OAAAC;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AAKA,QAAM,gBAAgB,6BAAqB;AAAA,IACzC,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,QAAM,sCAAsC,0BAAkB,iBAAiB;AAC/E,QAAM,iCAAiC,0BAAkB,sBAAsB;AAC/E,QAAM,oBAAoB,aAAa,aAAa;AAMpD,QAAM,gBAAgB,aAAK,uDAAmB,WAAW,mDAAiB,WAAW,WAAW,iEAAwB,WAAW,uDAAmB,SAAS;AAC/J,QAAM,cAAc;AAAA,IAClB,GAAG,uDAAmB;AAAA,IACtB,GAAG,mDAAiB;AAAA,IACpB,GAAG,iEAAwB;AAAA,IAC3B,GAAG,uDAAmB;AAAA,EACxB;AACA,QAAM,QAAQ;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,YAAY;AAAA,EACpB;AACA,MAAI,OAAO,KAAK,WAAW,EAAE,SAAS,GAAG;AACvC,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,kBAAkB;AAAA,EACjC;AACF;AACA,IAAO,yBAAQ;;;ACrEA,SAAR,QAOP,MAAM,YAAY;AAChB,QAAM;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,6BAA6B;AAAA,IAC7B,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,QAAQ;AAAA,MACN,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,cAAc,MAAM,IAAI,KAAK;AAInC,QAAM,0BAA0B,8BAAsB,UAAU,IAAI,GAAG,UAAU;AACjF,QAAM;AAAA,IACJ,OAAO;AAAA,MACL,WAAW;AAAA,MACX,GAAG;AAAA,IACL;AAAA,IACA;AAAA,EACF,IAAI,uBAAe;AAAA,IACjB;AAAA,IACA,GAAG;AAAA,IACH,wBAAwB,SAAS,SAAS,QAAQ;AAAA,IAClD,mBAAmB;AAAA,EACrB,CAAC;AACD,QAAM,MAAM,WAAW,aAAa,mEAAyB,KAAK,WAAW,GAAG;AAChF,QAAM,gBAAgB,SAAS,SAAS,iBAAiB,gBAAgB;AACzE,QAAM,QAAQ,yBAAiB,aAAa;AAAA,IAC1C,GAAI,SAAS,UAAU,CAAC,iBAAiB,CAAC,MAAM,IAAI,KAAK;AAAA,IACzD,GAAI,SAAS,UAAU,CAAC,MAAM,IAAI,KAAK;AAAA,IACvC,GAAG;AAAA,IACH,GAAI,iBAAiB,CAAC,8BAA8B;AAAA,MAClD,IAAI;AAAA,IACN;AAAA,IACA,GAAI,iBAAiB,8BAA8B;AAAA,MACjD,WAAW;AAAA,IACb;AAAA,IACA;AAAA,EACF,GAAG,UAAU;AACb,SAAO,CAAC,aAAa,KAAK;AAC5B;", "names": ["joinedClasses", "mergedStyle", "props"]}