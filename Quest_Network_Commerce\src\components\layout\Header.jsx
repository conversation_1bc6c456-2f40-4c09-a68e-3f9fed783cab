import React from 'react';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import {
  AppB<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>po<PERSON>, Button, Box, IconButton, TextField, InputAdornment, Badge, Menu, MenuItem, Breadcrumbs, Link, Drawer, List, ListItem, ListItemIcon, ListItemText, Divider, Dialog, DialogTitle, DialogContent, Radio, RadioGroup, FormControlLabel, Autocomplete, CircularProgress
} from '@mui/material';
import HomeIcon from '@mui/icons-material/Home';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import StorefrontIcon from '@mui/icons-material/Storefront';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SearchIcon from '@mui/icons-material/Search';
import CameraAltOutlinedIcon from '@mui/icons-material/CameraAltOutlined';
import MicNoneOutlinedIcon from '@mui/icons-material/MicNoneOutlined';
import LanguageIcon from '@mui/icons-material/Language';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LogoutIcon from '@mui/icons-material/Logout';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import MenuIcon from '@mui/icons-material/Menu';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { useTheme } from '@mui/material/styles';
import logo from '../../assets/Images/HCLSoftwareLogoBlack.png';
import { HelpCenterRounded } from '@mui/icons-material';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import CloseIcon from '@mui/icons-material/Close';
import RoomIcon from '@mui/icons-material/Room';
import { masterProductList } from '../../data/masterProductList';

// Breadcrumb mapping for top-level and nested menus
const breadcrumbNameMap = {
  '/': 'Home',
  '/products': 'Products',
  '/wishlist': 'Wishlist',
  '/cart': 'Cart',
  '/stores': 'Stores',
  '/orders': 'Orders',
  '/account': 'My Account',
  '/account/profile': 'My Profile',
  '/account/security': 'Login & Security',
  '/account/addresses': 'Saved Addresses',
  '/account/payment-options': 'Saved Payment Options',
  '/account/saved-searches': 'Saved Searches',
  '/account/my-reviews': 'My Reviews',
  '/account/my-questions': 'My Questions & Answers',
  '/account/notification-preferences': 'Notification Preferences',
  '/account/language-currency': 'Language & Currency',
};

const navLinks = [
  { to: '/home', icon: <HomeIcon />, label: 'Home' },
  { to: '/wishlist', icon: <FavoriteIcon />, label: 'Wishlist' },
  { to: '/cart', icon: <ShoppingCartIcon />, label: 'Cart' },
  { to: '/stores', icon: <StorefrontIcon />, label: 'Stores' },
  { to: '/orders', icon: <ReceiptLongIcon />, label: 'Orders' },
];

// Update moreMenuItems to use relevant icons for each destination
const moreMenuItems = [
  { label: 'Rewards', icon: <EmojiEventsIcon />, to: '/rewards' },
  { label: 'Support', icon: <HelpOutlineIcon />, to: '/support' },
  // { label: 'Notifications', icon: <NotificationsActiveIcon />, to: '/notifications' },
  // { label: 'Returns', icon: <ReceiptLongIcon />, to: '/returns' },
  { label: 'Logout', icon: <LogoutIcon />, to: '/login' },
];

const userMenuItems = [
  { label: 'My Account', icon: <AccountCircleIcon />, to: '/account' },
  { label: 'My Activity', icon: <TrendingUpIcon />, to: '/account/activity' },
  { label: 'Rewards Zone', icon: <EmojiEventsIcon />, to: '/account/rewards' },
  { label: 'Wallet & Gift Cards', icon: <AccountBalanceWalletIcon />, to: '/account/wallet' },
  { label: 'Alerts Preferences', icon: <NotificationsActiveIcon />, to: '/account/notifications' },
  { label: 'Help & Support', icon: <HelpOutlineIcon />, to: '/account/support' },
];

const languageOptions = [
  { value: 'en', code: 'EN', label: 'English', sub: '(English)', flag: '🇬🇧' },
  { value: 'fr', code: 'FR', label: 'Français', sub: '(French)', flag: '🇫🇷' },
  { value: 'zh', code: 'ZH', label: '中文', sub: '(Chinese)', flag: '🇨🇳' },
  { value: 'ja', code: 'JA', label: '日本語', sub: '(Japanese)', flag: '🇯🇵' },
  { value: 'ar', code: 'AR', label: 'العربية', sub: '(Arabic)', flag: '🇸🇦' },
];

// Helper to highlight matched text
function highlightMatch(text, query) {
  if (!query) return text;
  const regex = new RegExp(`(${query})`, 'ig');
  const parts = text.split(regex);
  return parts.map((part, i) =>
    regex.test(part)
      ? <span key={i} style={{ background: '#00bcd44d', color: '#0097a7', fontWeight: 700 }}>{part}</span>
      : part
  );
}

const Header = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [languageDialogOpen, setLanguageDialogOpen] = React.useState(false);
  const [locationDialogOpen, setLocationDialogOpen] = React.useState(false);
  const [selectedLanguage, setSelectedLanguage] = React.useState('en');
  const [selectedCity, setSelectedCity] = React.useState('Detecting...');
  const [selectedPincode, setSelectedPincode] = React.useState('');
  const [searchValue, setSearchValue] = React.useState(null); // Now store the selected product object
  const [searchInput, setSearchInput] = React.useState('');
  const [searchOptions, setSearchOptions] = React.useState([]);
  const [locating, setLocating] = React.useState(false);

  // Helper to get current language label+flag
  const currentLang = languageOptions.find(l => l.value === selectedLanguage) || languageOptions[0];

  const handleMenu = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  const location = useLocation();

  // Breadcrumbs logic
  const pathnames = location.pathname.split('/').filter((x) => x);
  const buildBreadcrumbs = () => {
    const crumbs = [];
    let path = '';
    pathnames.forEach((value, idx) => {
      path += '/' + value;
      const isLast = idx === pathnames.length - 1;

      let name;
      let linkPath = path;

      // Handle special cases for product detail pages
      if (pathnames[idx - 1] === 'product' && isLast) {
        // This is a product ID, find the product name
        const product = masterProductList.find(p => p.id === value);
        name = product ? product.name.en : value.charAt(0).toUpperCase() + value.slice(1);
        // Keep the current path for product detail
      } else if (value === 'product') {
        // Change 'product' to 'Products' and link to products page
        name = 'Products';
        linkPath = '/products';
      } else {
        // Use mapping or capitalize first letter
        name = breadcrumbNameMap[path] || value.charAt(0).toUpperCase() + value.slice(1);
      }

      crumbs.push({
        to: linkPath,
        name,
        isLast,
      });
    });
    return crumbs;
  };
  const breadcrumbs = buildBreadcrumbs();

  // Responsive drawer for mobile nav
  const handleDrawerToggle = () => setMobileOpen(!mobileOpen);

  const mobileNavMain = [
    { to: '/', icon: <HomeIcon />, label: 'Home' },
    { to: '/wishlist', icon: <FavoriteIcon />, label: 'Wishlist' },
    { to: '/cart', icon: <ShoppingCartIcon />, label: 'Cart' },
    { to: '/stores', icon: <StorefrontIcon />, label: 'Stores' },
    { to: '/orders', icon: <ReceiptLongIcon />, label: 'Orders' },
    { label: 'Notifications', icon: <Badge badgeContent={7} color="error" overlap="circular"><NotificationsIcon /></Badge>, to: '/notifications' },
  ];
  const mobileNavStuff = [
    { label: 'My Account', icon: <AccountCircleIcon />, to: '/account' },
    { label: 'Rewards Zone', icon: <EmojiEventsIcon />, to: '/account/rewards' },
    { label: 'Help & Support', icon: <HelpCenterRounded />, to: '/account/support' },
  ];
  const mobileNavLogout = { label: 'Logout', icon: <LogoutIcon />, to: '/logout' };

  const mobileNavLinks = [
    { to: '/wishlist', icon: <FavoriteIcon />, label: 'Wishlist' },
    { to: '/cart', icon: <ShoppingCartIcon />, label: 'Cart' },
    { to: '/orders', icon: <ReceiptLongIcon />, label: 'Orders' },
    { label: 'Notifications', icon: <Badge badgeContent={7} color="error" overlap="circular"><NotificationsIcon /></Badge>, to: '/notifications' },
  ];

  const drawer = (
    <Box sx={{ width: 260, p: 0, height: '100%', display: 'flex', flexDirection: 'column' }} role="presentation" onClick={handleDrawerToggle}>
      {/* User Profile Section */}
      <Box sx={{ bgcolor: theme.palette.primary.main, color: '#fff', px: 2, py: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
        <AccountCircleIcon sx={{ fontSize: 44, bgcolor: '#fff', color: theme.palette.primary.main, borderRadius: '50%', p: 0.5 }} />
        <Box>
          <Typography variant="subtitle1" sx={{ fontWeight: 700, lineHeight: 1, fontSize: 18 }}>User</Typography>
          <Typography variant="caption" sx={{ color: '#e0f7fa', fontWeight: 400, fontSize: 13 }}>Bengaluru</Typography>
        </Box>
      </Box>
      {/* Main Nav */}
      <List sx={{ py: 0.5 }}>
        {mobileNavMain.map((item) => (
          <ListItem button component={RouterLink} to={item.to} key={item.label} sx={{ py: 1, px: 2 }}>
            <ListItemIcon sx={{ color: theme.palette.primary.main }}>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} primaryTypographyProps={{ fontWeight: 500, fontSize: 16 }} />
          </ListItem>
        ))}
      </List>
      <Divider sx={{ my: 1 }} />
      {/* My Stuff Section */}
      <Typography variant="overline" sx={{ color: 'text.secondary', fontWeight: 700, px: 2, letterSpacing: 1, fontSize: 13 }}>
        MY STUFF
      </Typography>
      <List sx={{ py: 0 }}>
        {mobileNavStuff.map((item) => (
          <ListItem button component={RouterLink} to={item.to} key={item.label} sx={{ py: 1, px: 2 }}>
            <ListItemIcon sx={{ color: theme.palette.primary.main }}>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} primaryTypographyProps={{ fontWeight: 500, fontSize: 16 }} />
          </ListItem>
        ))}
      </List>
      <Divider sx={{ my: 1 }} />
      {/* Logout at bottom, but in normal flow with margin */}
      <List sx={{ mt: 'auto', mb: 1 }}>
        <ListItem button component={RouterLink} to={mobileNavLogout.to} key={mobileNavLogout.label} sx={{ py: 1.5, px: 2 }}>
          <ListItemIcon sx={{ color: theme.palette.primary.main }}>{mobileNavLogout.icon}</ListItemIcon>
          <ListItemText primary={mobileNavLogout.label} primaryTypographyProps={{ fontWeight: 500, fontSize: 16 }} />
        </ListItem>
      </List>
    </Box>
  );

  // Get current path for active nav
  const currentPath = location.pathname;

  // Search filter logic (use masterProductList)
  React.useEffect(() => {
    if (searchInput && searchInput.length > 0) {
      const input = searchInput.toLowerCase();
      const filtered = masterProductList.filter(product => {
        const name = product.name?.en?.toLowerCase() || '';
        const brand = product.brand?.en?.toLowerCase() || '';
        const category = product.category?.en?.toLowerCase() || '';
        return (
          name.includes(input) ||
          brand.includes(input) ||
          category.includes(input)
        );
      });
      setSearchOptions(filtered);
    } else {
      setSearchOptions([]);
    }
    // Do NOT fire custom event here anymore
  }, [searchInput]);

  // Handle search selection and navigation
  const handleAutocompleteChange = (event, newValue) => {
    setSearchValue(newValue);
    if (newValue && newValue.name && newValue.name.en) {
      // Navigate to products page with search query
      navigate(`/products?search=${encodeURIComponent(newValue.name.en)}`);

      // Also fire custom event for backward compatibility
      const customEvent = new CustomEvent('header-search', { detail: newValue.name.en });
      window.dispatchEvent(customEvent);
    } else if (searchInput && searchInput.trim()) {
      // If user typed something but didn't select from dropdown, search for the typed text
      navigate(`/products?search=${encodeURIComponent(searchInput.trim())}`);

      // Also fire custom event for backward compatibility
      const customEvent = new CustomEvent('header-search', { detail: searchInput.trim() });
      window.dispatchEvent(customEvent);
    } else {
      // Clear search if no value
      const customEvent = new CustomEvent('header-search', { detail: '' });
      window.dispatchEvent(customEvent);
    }
  };

  // Handle Enter key press in search field
  const handleSearchKeyDown = (event) => {
    if (event.key === 'Enter' && searchInput && searchInput.trim()) {
      event.preventDefault();
      navigate(`/products?search=${encodeURIComponent(searchInput.trim())}`);

      // Also fire custom event for backward compatibility
      const customEvent = new CustomEvent('header-search', { detail: searchInput.trim() });
      window.dispatchEvent(customEvent);
    }
  };

  // Fetch city and pincode from coordinates using Nominatim
  const fetchCityFromCoords = async (lat, lon) => {
    try {
      const res = await fetch(`https://nominatim.openstreetmap.org/reverse?lat=${lat}&lon=${lon}&format=json`);
      const data = await res.json();
      setSelectedPincode(data.address.postcode || '');
      return (
        data.address.city ||
        data.address.town ||
        data.address.village ||
        data.address.state ||
        'Unknown'
      );
    } catch {
      setSelectedPincode('');
      return 'Unknown';
    }
  };

  // On mount, try to get location automatically
  React.useEffect(() => {
    setLocating(true);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          const city = await fetchCityFromCoords(latitude, longitude);
          setSelectedCity(city);
          setLocating(false);
        },
        () => {
          setSelectedCity('Unknown');
          setSelectedPincode('');
          setLocating(false);
        }
      );
    } else {
      setSelectedCity('Unknown');
      setSelectedPincode('');
      setLocating(false);
    }
  }, []);

  // Handler for manual location fetch
  const handleLocationClick = () => {
    setLocating(true);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          const city = await fetchCityFromCoords(latitude, longitude);
          setSelectedCity(city);
          setLocating(false);
        },
        () => {
          setSelectedCity('Unknown');
          setSelectedPincode('');
          setLocating(false);
          alert('Unable to retrieve your location.');
        }
      );
    } else {
      setSelectedCity('Unknown');
      setSelectedPincode('');
      setLocating(false);
      alert('Geolocation is not supported by your browser.');
    }
  };

  return (
    <Box sx={{ position: 'fixed', top: 0, left: 0, width: '100vw', zIndex: 1202, bgcolor: '#fff' }}>
      <AppBar position="static" elevation={0} sx={{ bgcolor: '#fff', color: '#111', width: '100vw', maxWidth: '100vw', left: 0, marginBottom: '0', boxShadow: 'none' }}>
        {/* Toolbar: logo, search, nav (desktop); logo, nav (mobile) */}
        <Toolbar
          sx={{
            minHeight: 54,
            mt: 2,
            px: { xs: 1, md: 4 },
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'space-between',
            width: '100vw',
            maxWidth: '100vw',
            gap: 2,
            flexWrap: { xs: 'wrap', md: 'nowrap' },
            bgcolor: '#fff',
          }}
        >
          {/* Left: Hamburger (mobile) */}
          <Box sx={{ display: { xs: 'flex', md: 'none' }, alignItems: 'center', mr: 1 }}>
            <IconButton color="inherit" edge="start" onClick={handleDrawerToggle}>
              <MenuIcon />
            </IconButton>
          </Box>
          {/* Logo, Location, and subtitle */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', minWidth: 0, flexShrink: 0, mr: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box component={RouterLink} to="/" sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none', height: 30 }}>
                <Box component="img" src={logo} alt="HCLSoftware" sx={{ height: 25, width: 'auto', display: 'block', mr: 1 }} />
              </Box>
              <Button
                startIcon={<RoomIcon sx={{ color: 'primary.main' }} />}
                onClick={handleLocationClick}
                sx={{
                  textTransform: 'none',
                  color: 'text.secondary',
                  fontWeight: 600,
                  fontSize: 15,
                  px: 1,
                  minWidth: 0,
                  background: 'transparent',
                  '&:hover': { background: 'rgba(0,0,0,0.04)' }
                }}
                disabled={locating}
              >
                {locating ? <CircularProgress size={16} sx={{ mr: 1 }} /> : null}
                {selectedCity}
                {selectedPincode ? `, ${selectedPincode}` : ''}
              </Button>
            </Box>
            <Typography variant="subtitle2" sx={{ color: 'text.secondary', fontWeight: 600, mt: 0.2, ml: 0, fontSize: 16, letterSpacing: 1 }}>
              Quest Network Commerce
            </Typography>
          </Box>
          {/* Search bar: in row for md+, below for xs */}
          <Box
            sx={{
              flex: 1,
              display: { xs: 'none', md: 'flex' },
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
              maxWidth: '100%',
              minWidth: 0,
              mx: 0,
            }}
          >
            <Box sx={{ width: '100%', maxWidth: 600, mx: 'auto' }}>
              <Autocomplete
                freeSolo
                options={searchOptions}
                getOptionLabel={option => option.name?.en || ''}
                filterOptions={x => x}
                value={searchValue}
                onChange={handleAutocompleteChange}
                onInputChange={(event, newInputValue) => setSearchInput(newInputValue)}
                fullWidth
                renderOption={(props, option) => (
                  <li {...props} key={option.id} style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: 8 }}>
                    <span style={{ fontWeight: 600, fontSize: 15 }}>
                      {highlightMatch(option.name.en, searchInput)}
                    </span>
                    <span style={{ fontSize: 13, color: '#666' }}>
                      {highlightMatch(option.brand?.en || '', searchInput)} &middot; {highlightMatch(option.category?.en || '', searchInput)}
                    </span>
                  </li>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    variant="outlined"
                    placeholder="Search products..."
                    size="small"
                    onKeyDown={handleSearchKeyDown}
                    sx={{
                      bgcolor: '#fafbfc',
                      borderRadius: 999,
                      border: '1px solid #e0e0e0',
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 999,
                        pr: 0,
                        height: 44,
                        fontSize: 16,
                        boxShadow: 'none',
                        border: 'none',
                      },
                      '& input': { py: 1.2, fontSize: 16 },
                    }}
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon sx={{ color: 'text.secondary' }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton size="small"><CameraAltOutlinedIcon fontSize="small" /></IconButton>
                          <IconButton size="small"><MicNoneOutlinedIcon fontSize="small" /></IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </Box>
          </Box>
          {/* Right: Wishlist & Cart (mobile), all nav (desktop) */}
          <Box sx={{ display: { xs: 'flex', md: 'none' }, alignItems: 'center', gap: 1, ml: 'auto' }}>
            {mobileNavLinks.map((item, idx) => {
              if (item.isUser) {
                return (
                  <IconButton key={item.label} color="inherit" onClick={handleMenu}>
                    <AccountCircleIcon />
                  </IconButton>
                );
              }
              return (
                <IconButton key={item.label} component={RouterLink} to={item.to} color="inherit">
                  {item.icon}
                </IconButton>
              );
            })}
          </Box>
          {/* Clean, consistent nav bar: single row, all items bottom-aligned, no extra backgrounds, modern spacing */}
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              alignItems: 'flex-end',
              gap: 2.2, // Reduce gap for more compact, uniform spacing
              flexGrow: 0,
              height: 54,
              bgcolor: 'transparent',
              px: 0,
            }}
          >
            {[...navLinks,
            { label: `${currentLang.code} | ${currentLang.label}`, icon: <LanguageIcon />, to: '/language', isLanguage: true },
            {
              label: 'Notifications', icon: <Badge badgeContent={7} color="error" overlap="circular"
                sx={{
                  position: 'relative',
                  top: 2,
                  '& .MuiBadge-badge': {
                    fontSize: 10,
                    height: 16,
                    minWidth: 16,
                    top: 0,
                    right: -10,
                    transform: 'none',
                  }
                }}>
                <NotificationsIcon sx={{ fontSize: 22, display: 'block', mx: 'auto', mb: 0, verticalAlign: 'middle' }} />
              </Badge>, to: '/notifications'
            },
            { label: 'Username', icon: <AccountCircleIcon />, to: '/account', isUser: true },
            { label: 'More', icon: <MenuIcon />, isMore: true },
            ].map((item, idx) => {
              const isActive = currentPath === item.to;
              if (item.isLanguage) {
                return (
                  <Button
                    key={item.label}
                    color="inherit"
                    onClick={() => setLanguageDialogOpen(true)}
                    sx={{
                      flexDirection: 'column',
                      minWidth: 0,
                      px: 0,
                      py: 0,
                      color: '#222',
                      fontWeight: 500,
                      borderRadius: 2,
                      textTransform: 'none',
                      fontSize: 13,
                      background: 'transparent',
                      boxShadow: 'none',
                      transition: 'color 0.2s',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 0.5,
                      height: 54,
                    }}
                    disableRipple
                  >
                    <LanguageIcon sx={{ fontSize: 22, display: 'block', mx: 'auto', mb: 0, verticalAlign: 'middle' }} />
                    <span style={{ fontSize: 12, marginLeft: 0, marginRight: 0, textAlign: 'center' }}>{currentLang.code} | {currentLang.label}</span>
                  </Button>
                );
              }
              if (item.isMore) {
                return (
                  <Box key={item.label} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'flex-end', minWidth: 0 }}>
                    <Button
                      color="inherit"
                      onClick={handleMenu}
                      sx={{
                        flexDirection: 'column',
                        minWidth: 0,
                        px: 0,
                        py: 0,
                        color: anchorEl ? theme.palette.primary.main : '#222',
                        fontWeight: anchorEl ? 700 : 500,
                        borderRadius: 2,
                        textTransform: 'none',
                        fontSize: 13,
                        background: 'transparent',
                        boxShadow: 'none',
                        transition: 'color 0.2s',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 0.5,
                        height: 54,
                      }}
                      disableRipple
                    >
                      <MenuIcon sx={{ fontSize: 22, display: 'block', mx: 'auto', mb: 0, color: anchorEl ? theme.palette.primary.main : '#222', transition: 'color 0.2s', verticalAlign: 'middle' }} />
                      <span style={{ fontSize: 12, marginLeft: 0, marginRight: 0, textAlign: 'center', color: anchorEl ? theme.palette.primary.main : '#222', fontWeight: anchorEl ? 700 : 500 }}>More</span>
                    </Button>
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl)}
                      onClose={handleClose}
                      PaperProps={{
                        sx: {
                          mt: 1,
                          minWidth: 160,
                          borderRadius: 2,
                          p: 0,
                          boxShadow: 3,
                          zIndex: 1400,
                          bgcolor: '#fff',
                          color: '#222',
                          border: `1px solid ${theme.palette.divider}`,
                        }
                      }}
                      MenuListProps={{ sx: { p: 0 } }}
                    >
                      {moreMenuItems.map((menu, i) => (
                        <MenuItem
                          key={menu.label}
                          component={RouterLink}
                          to={menu.to}
                          onClick={handleClose}
                          sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 1.5,
                            px: 2,
                            py: 1,
                            color: currentPath === menu.to ? theme.palette.primary.main : '#222',
                            fontWeight: currentPath === menu.to ? 700 : 500,
                            fontSize: 14,
                            borderRadius: 1,
                            '&:hover': {
                              color: theme.palette.primary.main,
                              bgcolor: 'rgba(0,0,0,0.03)',
                            },
                          }}
                        >
                          {React.cloneElement(menu.icon, { fontSize: 'medium', sx: { fontSize: 22, color: currentPath === menu.to ? theme.palette.primary.main : '#222' } })}
                          <span style={{ fontSize: 14 }}>{menu.label}</span>
                        </MenuItem>
                      ))}
                    </Menu>
                  </Box>
                );
              }
              // All other nav items (including Username) use the same style
              return (
                <Button
                  key={item.label}
                  component={RouterLink}
                  to={item.to}
                  color="inherit"
                  sx={{
                    flexDirection: 'column',
                    minWidth: 0,
                    px: 0,
                    py: 0,
                    color: isActive ? theme.palette.primary.main : '#222',
                    fontWeight: isActive ? 700 : 500,
                    borderRadius: 2,
                    textTransform: 'none',
                    fontSize: 13,
                    background: 'transparent',
                    boxShadow: 'none',
                    transition: 'color 0.2s',
                    '&:hover': {
                      color: theme.palette.primary.main,
                      bgcolor: 'transparent',
                    },
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 0.5,
                    height: 54,
                  }}
                  disableRipple
                >
                  {React.cloneElement(item.icon, { fontSize: 'medium', sx: { fontSize: 22, display: 'block', mx: 'auto', mb: 0, color: isActive ? theme.palette.primary.main : '#222', verticalAlign: 'middle' } })}
                  <span style={{ fontSize: 12, marginLeft: 0, marginRight: 0, textAlign: 'center', color: isActive ? theme.palette.primary.main : '#222', fontWeight: isActive ? 700 : 500 }}>{item.label}</span>
                </Button>
              );
            })}
          </Box>
        </Toolbar>
        {/* Search bar: only for mobile, below top row */}
        <Box sx={{ width: '100%', px: 1, py: 1, display: { xs: 'block', md: 'none' }, bgcolor: '#fff' }}>
          <Box sx={{ width: '100%' }}>
            <Autocomplete
              freeSolo
              options={searchOptions}
              getOptionLabel={option => option.name?.en || ''}
              filterOptions={x => x}
              value={searchValue}
              onChange={handleAutocompleteChange}
              onInputChange={(event, newInputValue) => setSearchInput(newInputValue)}
              fullWidth
              renderOption={(props, option) => (
                <li {...props} key={option.id} style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: 8 }}>
                  <span style={{ fontWeight: 600, fontSize: 15 }}>
                    {highlightMatch(option.name.en, searchInput)}
                  </span>
                  <span style={{ fontSize: 13, color: '#666' }}>
                    {highlightMatch(option.brand?.en || '', searchInput)} &middot; {highlightMatch(option.category?.en || '', searchInput)}
                  </span>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  variant="outlined"
                  placeholder="Search products..."
                  size="small"
                  onKeyDown={handleSearchKeyDown}
                  sx={{
                    bgcolor: '#fafbfc',
                    borderRadius: 999,
                    border: '1px solid #e0e0e0',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 999,
                      pr: 0,
                      height: 44,
                      fontSize: 16,
                      boxShadow: 'none',
                      border: 'none',
                    },
                    '& input': { py: 1.2, fontSize: 16 },
                  }}
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon sx={{ color: 'text.secondary' }} />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton size="small"><CameraAltOutlinedIcon fontSize="small" /></IconButton>
                        <IconButton size="small"><MicNoneOutlinedIcon fontSize="small" /></IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          </Box>
        </Box>
      </AppBar>
      {/* Mobile Drawer */}
      <Drawer anchor="left" open={mobileOpen} onClose={handleDrawerToggle}
        sx={{
          display: { xs: 'block', md: 'none' },
          mt: { xs: '64px', md: 0 },
          zIndex: 1401, // Ensure drawer is above AppBar and Breadcrumbs
          '& .MuiDrawer-paper': {
            bgcolor: '#fff',
            color: '#222',
            borderRight: `1px solid ${theme.palette.divider}`,
            boxShadow: 3,
            zIndex: 1401,
          }
        }}
      >
        {drawer}
      </Drawer>
      {/* Breadcrumbs below AppBar and search bar */}
      <Box sx={{ bgcolor: '#f9fbfc', px: { xs: 1, md: 4 }, py: 1, borderBottom: '1px solid #e5e7eb', width: '100vw', maxWidth: '100vw', overflowX: 'auto', minHeight: { xs: 44, md: 'unset' } }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link
            component={RouterLink}
            underline="hover"
            color="inherit"
            to="/"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" /> Home
          </Link>
          {breadcrumbs.map((crumb) =>
            crumb.isLast ? (
              <Typography color="text.primary" key={crumb.to} sx={{ fontWeight: 500 }}>
                {crumb.name}
              </Typography>
            ) : (
              <Link
                component={RouterLink}
                underline="hover"
                color="inherit"
                to={crumb.to}
                key={crumb.to}
              >
                {crumb.name}
              </Link>
            )
          )}
        </Breadcrumbs>
      </Box>
      <Dialog open={languageDialogOpen} onClose={() => setLanguageDialogOpen(false)} maxWidth="xs" fullWidth>
        <DialogTitle sx={{ fontWeight: 700, fontSize: 26, pb: 1, pr: 5 }}>
          Select language
          <IconButton
            aria-label="close"
            onClick={() => setLanguageDialogOpen(false)}
            sx={{ position: 'absolute', right: 12, top: 12, color: '#888' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 1, pb: 3 }}>
          <RadioGroup
            row
            value={selectedLanguage}
            onChange={e => {
              setSelectedLanguage(e.target.value);
              setLanguageDialogOpen(false);
            }}
            sx={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: 2,
              mt: 1,
            }}
          >
            {languageOptions.map(opt => (
              <FormControlLabel
                key={opt.value}
                value={opt.value}
                control={<Radio sx={{ '&.Mui-checked': { color: theme.palette.primary.main } }} />}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                      <span style={{ fontWeight: 600, fontSize: 17 }}>{opt.code} | {opt.label}</span>
                      <span style={{ fontSize: 14, color: '#666' }}>{opt.sub}</span>
                    </Box>
                  </Box>
                }
                sx={{
                  m: 0,
                  px: 1.5,
                  py: 1.2,
                  borderRadius: 2,
                  width: '100%',
                  '&.MuiFormControlLabel-root': {
                    background: selectedLanguage === opt.value ? '#f3f6fa' : 'transparent',
                    border: selectedLanguage === opt.value ? `1.5px solid ${theme.palette.primary.light}` : '1.5px solid transparent',
                    boxShadow: selectedLanguage === opt.value ? '0 2px 8px 0 rgba(0,0,0,0.04)' : 'none',
                  },
                }}
              />
            ))}
          </RadioGroup>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default Header;