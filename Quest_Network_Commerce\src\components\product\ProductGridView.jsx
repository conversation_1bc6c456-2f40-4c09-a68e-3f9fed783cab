import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  IconButton,

  Card,
  CardMedia,
  CardContent,
  CardActions,
  Chip,
  Paper,
  Stack,
  Rating,
  Badge,
  Tooltip,
  Fade,
  Zoom,
  Avatar,
  Divider
} from '@mui/material';
import GridViewIcon from '@mui/icons-material/GridView';
import ListIcon from '@mui/icons-material/List';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import StarIcon from '@mui/icons-material/Star';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { masterProductList } from '../../data/masterProductList';
import { useNavigate } from 'react-router-dom';

function ProductGridView({ setViewMode, filters, isSmallScreen, setSelectedProduct }) {
  const navigate = useNavigate();
  const [wishlist, setWishlist] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState(masterProductList);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      let result = [...masterProductList];

      // Apply all filters only if at least one filter is set
      const hasAnyFilter = Object.values(filters).some(
        (v) => Array.isArray(v) ? v.length > 0 : v && v !== '' && v !== false && v !== 0 && v !== 'all'
      );

      if (hasAnyFilter) {
        if (filters.searchVin) {
          result = result.filter((product) =>
            product.vinCompatible && product.vinCompatible.includes(filters.searchVin)
          );
        }
        if (filters.makes.length > 0) {
          result = result.filter((product) =>
            product.vehicleCompatibility.some((v) => filters.makes.includes(v.make))
          );
        }
        if (filters.models.length > 0) {
          result = result.filter((product) =>
            product.vehicleCompatibility.some((v) => filters.models.includes(v.model))
          );
        }
        if (filters.years.length > 0) {
          result = result.filter((product) =>
            product.vehicleCompatibility.some((v) => filters.years.includes(String(v.year)))
          );
        }
        if (filters.categories.length > 0) {
          result = result.filter((product) => filters.categories.includes(product.category.en));
        }
        if (filters.brands.length > 0) {
          result = result.filter((product) => filters.brands.includes(product.brand.en));
        }
        if (filters.materials.length > 0) {
          result = result.filter((product) => filters.materials.includes(product.material.en));
        }
        if (filters.warranties.length > 0) {
          result = result.filter((product) => filters.warranties.includes(product.warranty.en));
        }
        if (filters.stockLocations.length > 0) {
          result = result.filter((product) =>
            product.stock.locations && filters.stockLocations.some((loc) => product.stock.locations.includes(loc))
          );
        }
        if (filters.priceRange && Array.isArray(filters.priceRange)) {
          result = result.filter((product) =>
            product.priceUSD >= filters.priceRange[0] && product.priceUSD <= filters.priceRange[1]
          );
        }
        if (filters.partType === 'primary') {
          result = result.filter((product) => product.partType === 'primary');
        }
        if (!filters.includeSuperseded) {
          result = result.filter((product) => !product.tags.en.includes('Superseded'));
        }
      }

      // Add searchText filter from filters
      if (filters.searchText && filters.searchText.length > 0) {
        const input = filters.searchText.toLowerCase();
        result = result.filter(product =>
          product.name?.en?.toLowerCase().includes(input) ||
          product.brand?.en?.toLowerCase().includes(input) ||
          product.category?.en?.toLowerCase().includes(input) ||
          product.partNumber?.toLowerCase().includes(input)
        );
      }

      setFilteredProducts(result);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [filters]);

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  const toggleWishlist = (productId) => {
    setWishlist((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  return (
    <Box sx={{ width: '100%', py: { xs: 1, sm: 2 }, px: { xs: 0, sm: 1 } }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: { xs: 1, sm: 2 },
          gap: { xs: 1, sm: 1.5 },
          px: { xs: 0, sm: 0 },
        }}
      >
        <Typography
          variant="h6"
          color="text.primary"
          sx={{ fontSize: { xs: '1rem', sm: '1.25rem' }, fontWeight: 600 }}
        >
          All Products ({filteredProducts.length})
        </Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: 1,
            alignItems: { xs: 'stretch', sm: 'center' },
            width: { xs: '100%', sm: 'auto' },
          }}
        >
        
        </Box>
       
      </Box>

      <Box
        sx={{
          width: '100%',
          maxWidth: '100%',
          p: { xs: 1, sm: 1.5, md: 2, lg: 2.5 }, // Progressive responsive padding
          display: 'grid',
          gridTemplateColumns: {
            xs: 'repeat(2, 1fr)',  // 2 columns on mobile
            sm: 'repeat(3, 1fr)',  // 3 columns on small screens
            md: 'repeat(4, 1fr)',  // 4 columns on medium screens
            lg: 'repeat(5, 1fr)',  // 5 columns on large screens
            xl: 'repeat(6, 1fr)'   // 6 columns on extra large screens
          },
          gap: { xs: 1, sm: 1.5, md: 2, lg: 2.5 }, // Progressive responsive gaps
          alignItems: 'stretch',
        }}
      >
        {filteredProducts.map((product) => (
          <Box
            key={product.id}
            sx={{
              // CSS Grid handles the layout, so we just need basic styling
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
              height: '100%'
            }}
          >
            <Card
              className="product-card"
              onClick={() => navigate(`/product/${product.id}`)}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                height: '100%',
                minHeight: { xs: 300, sm: 340, md: 380, lg: 400, xl: 420 }, // Progressive responsive heights
                borderRadius: { xs: 1, sm: 1.5, md: 2, lg: 2.5 }, // Progressive border radius
                overflow: 'hidden',
                background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                border: '1px solid rgba(46, 192, 203, 0.08)',
                boxShadow: {
                  xs: '0 1px 4px rgba(46, 192, 203, 0.08)',
                  sm: '0 2px 8px rgba(46, 192, 203, 0.08)',
                  md: '0 4px 12px rgba(46, 192, 203, 0.08)'
                },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                cursor: 'pointer',
                position: 'relative',
                '&:hover': {
                  transform: {
                    xs: 'translateY(-2px)',
                    sm: 'translateY(-4px)',
                    md: 'translateY(-6px) scale(1.01)',
                    lg: 'translateY(-8px) scale(1.02)'
                  },
                  boxShadow: {
                    xs: '0 4px 12px rgba(46, 192, 203, 0.15)',
                    sm: '0 8px 24px rgba(46, 192, 203, 0.15)',
                    md: '0 12px 32px rgba(46, 192, 203, 0.2)'
                  },
                  border: '1px solid rgba(46, 192, 203, 0.2)',
                  '& .product-image': {
                    transform: 'scale(1.03)',
                  }
                },
              }}
            >
              <Box
                className="card-image"
                sx={{
                  position: 'relative',
                  background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                  height: { xs: 140, sm: 160, md: 180 },
                  width: '100%',
                  flexShrink: 0,
                  overflow: 'hidden',
                }}
              >
                <CardMedia
                  component="img"
                  image={product.imageUrl?.replace('./Assets/Images/', '/src/assets/images/') || '/src/assets/images/placeholder.jpg'}
                  alt={product.name.en}
                  className="product-image"
                  onError={(e) => {
                    e.target.src = '/src/assets/images/placeholder.jpg';
                  }}
                  sx={{
                    height: '100%',
                    width: '100%',
                    objectFit: 'cover',
                    transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  }}
                />


                {/* Modern Product Badges */}
                <Stack
                  spacing={0.5}
                  sx={{
                    position: 'absolute',
                    top: 12,
                    left: 12,
                    zIndex: 3,
                  }}
                >
                  {product.tags.en.map((tag, index) => (
                    <Fade in timeout={300 + index * 100} key={tag}>
                      <Chip
                        icon={
                          tag === 'Best Seller' ? <StarIcon sx={{ fontSize: 14 }} /> :
                          tag === 'Trending' ? <TrendingUpIcon sx={{ fontSize: 14 }} /> :
                          tag === 'Special Offer' ? <LocalOfferIcon sx={{ fontSize: 14 }} /> :
                          undefined
                        }
                        label={tag}
                        size="small"
                        sx={{
                          height: 24,
                          borderRadius: 3,
                          color: 'white',
                          fontWeight: 600,
                          fontSize: '0.7rem',
                          background:
                            tag === 'Best Seller'
                              ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
                              : tag === 'Trending'
                              ? 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)'
                              : tag === 'Popular'
                              ? 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)'
                              : tag === 'Special Offer'
                              ? 'linear-gradient(135deg, #ffd43b 0%, #fab005 100%)'
                              : 'linear-gradient(135deg, #868e96 0%, #495057 100%)',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          '& .MuiChip-icon': {
                            color: 'white',
                            marginLeft: 0.5,
                          },
                          '&:hover': {
                            transform: 'scale(1.05)',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                          },
                          transition: 'all 0.2s ease',
                        }}
                      />
                    </Fade>
                  ))}
                </Stack>
                {/* Modern Wishlist Button */}
                <Tooltip title={wishlist.includes(product.id) ? 'Remove from wishlist' : 'Add to wishlist'} arrow>
                  <IconButton
                    className="wishlist-icon"
                    sx={{
                      position: 'absolute',
                      top: 12,
                      right: 12,
                      zIndex: 4,
                      width: 40,
                      height: 40,
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      color: wishlist.includes(product.id) ? '#ff6b6b' : '#868e96',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      '&:hover': {
                        transform: 'scale(1.1)',
                        bgcolor: 'white',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                        color: wishlist.includes(product.id) ? '#ff5252' : '#ff6b6b',
                      },
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleWishlist(product.id);
                    }}
                  >
                    {wishlist.includes(product.id) ?
                      <FavoriteIcon sx={{ fontSize: 20 }} /> :
                      <FavoriteBorderIcon sx={{ fontSize: 20 }} />
                    }
                  </IconButton>
                </Tooltip>
              </Box>
              <CardContent
                sx={{
                  flex: 1,
                  p: { xs: 1, sm: 1.5, md: 2 },
                  display: 'flex',
                  flexDirection: 'column',
                  gap: { xs: 0.5, sm: 1 },
                }}
              >
                {/* Product Info */}
                <Box>
                  <Typography
                    variant="h6"
                    sx={{
                      color: 'text.primary',
                      fontWeight: 700,
                      fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
                      lineHeight: 1.2,
                      mb: { xs: 0.25, sm: 0.5 },
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {product.name.en}
                  </Typography>

                  <Stack direction="row" alignItems="center" spacing={0.5} sx={{ mb: { xs: 0.5, sm: 1 } }}>
                    <Chip
                      label={product.partNumber}
                      size="small"
                      variant="outlined"
                      sx={{
                        height: { xs: 18, sm: 20 },
                        fontSize: { xs: '0.6rem', sm: '0.7rem' },
                        borderColor: 'primary.main',
                        color: 'primary.main',
                        '& .MuiChip-label': { px: { xs: 0.5, sm: 1 } }
                      }}
                    />
                    <Chip
                      label={product.stock.status}
                      size="small"
                      sx={{
                        height: { xs: 18, sm: 20 },
                        fontSize: { xs: '0.6rem', sm: '0.7rem' },
                        fontWeight: 600,
                        bgcolor:
                          product.stock.status === 'In Stock'
                            ? 'rgba(76, 175, 80, 0.1)'
                            : product.stock.status === 'Low Stock'
                            ? 'rgba(255, 193, 7, 0.1)'
                            : 'rgba(244, 67, 54, 0.1)',
                        color:
                          product.stock.status === 'In Stock'
                            ? '#4caf50'
                            : product.stock.status === 'Low Stock'
                            ? '#ff9800'
                            : '#f44336',
                        border: `1px solid ${
                          product.stock.status === 'In Stock'
                            ? '#4caf50'
                            : product.stock.status === 'Low Stock'
                            ? '#ff9800'
                            : '#f44336'
                        }`,
                      }}
                    />
                  </Stack>

                  {/* Rating */}
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Rating
                      value={4.5}
                      precision={0.5}
                      size="small"
                      readOnly
                      sx={{
                        '& .MuiRating-iconFilled': {
                          color: '#ffd700',
                        },
                        '& .MuiRating-iconEmpty': {
                          color: '#e0e0e0',
                        },
                      }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      (4.5)
                    </Typography>
                  </Stack>
                </Box>

                {/* Price Section */}
                <Box>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography
                        variant="h5"
                        sx={{
                          color: 'primary.main',
                          fontWeight: 800,
                          fontSize: '1.2rem',
                          lineHeight: 1,
                        }}
                      >
                        ${product.priceUSD.toFixed(2)}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'text.secondary',
                          fontSize: '0.75rem',
                          textDecoration: 'line-through'
                        }}
                      >
                        ${(product.priceUSD * 1.2).toFixed(2)}
                      </Typography>
                    </Box>
                    <Chip
                      label="20% OFF"
                      size="small"
                      sx={{
                        bgcolor: 'error.main',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '0.7rem',
                      }}
                    />
                  </Stack>
                </Box>
              </CardContent>

              {/* Enhanced Card Actions */}
              <CardActions
                sx={{
                  p: 2,
                  pt: 0,
                  gap: 1,
                }}
              >
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<ShoppingCartIcon />}
                  sx={{
                    background: 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)',
                    color: 'white',
                    fontWeight: 600,
                    borderRadius: 2,
                    textTransform: 'none',
                    fontSize: '0.9rem',
                    py: 1,
                    boxShadow: '0 4px 12px rgba(46, 192, 203, 0.3)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #23A3AD 0%, #1e8e96 100%)',
                      transform: 'translateY(-1px)',
                      boxShadow: '0 6px 16px rgba(46, 192, 203, 0.4)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    // Add to cart logic
                  }}
                >
                  Add to Cart
                </Button>
              </CardActions>
            </Card>
          </Box>
        ))}
      </Box>
    </Box>
  );
}

export default ProductGridView;