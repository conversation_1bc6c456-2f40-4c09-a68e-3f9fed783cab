import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON>po<PERSON>,
    TextField,
    Button,
    FormControlLabel,
    Checkbox,
    Slider,
    Divider,
    InputAdornment,
    IconButton,
    Chip,
    Stack,
    Tooltip,
    Grid,
    Card,
    CardContent,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Badge,
    Fade,
    Collapse,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Paper,
    Switch,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Autocomplete
} from '@mui/material';
import {
    Search as SearchIcon,
    Mic as MicIcon,
    CameraAlt as CameraAltIcon,
    ShoppingCart as ShoppingCartIcon,
    Save as SaveIcon,
    RestartAlt as RestartAltIcon,
    ExpandMore as ExpandMoreIcon,
    FilterList as FilterListIcon,
    Clear as ClearIcon,
    Tune as TuneIcon,
    CheckBox as CheckBoxIcon,
    CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
    LocalOffer as LocalOfferIcon,
    Category as CategoryIcon,
    DirectionsCar as DirectionsCarIcon,
    Build as BuildIcon,
    Star as StarIcon,
    LocationOn as LocationOnIcon,
    AttachMoney as AttachMoneyIcon
} from '@mui/icons-material';
import { masterProductList } from '../../data/masterProductList';

function FiltersSidebar({ setImageSearchOpen, setBarcodeScannerOpen, setBulkOrderOpen, filters, setFilters, fullScreen = false }) {
    const [searchFilters, setSearchFilters] = useState({
        makes: '',
        models: '',
        years: '',
        categories: '',
        brands: '',
        materials: '',
        warranties: '',
        stockLocations: '',
    });

    // Modern accordion state management
    const [expandedSections, setExpandedSections] = useState({
        vehicle: true,
        category: true,
        price: false,
        features: false,
        location: false
    });

    // State to store unique options dynamically
    const [filterOptions, setFilterOptions] = useState({
        makes: [],
        models: [],
        years: [],
        categories: [],
        brands: [],
        materials: [],
        warranties: [],
        stockLocations: [],
    });

    // Initial filter state for comparison
    const initialFilters = {
        makes: [],
        models: [],
        years: [],
        categories: [],
        brands: [],
        materials: [],
        warranties: [],
        stockLocations: [],
        priceRange: [0, 1000],
        searchVin: '',
        partType: '',
        includeSuperseded: false,
    };

    // Mapping of chip type to filters key
    const typeToKeyMap = {
        make: 'makes',
        model: 'models',
        year: 'years',
        category: 'categories',
        brand: 'brands',
        material: 'materials',
        warranty: 'warranties',
        'stock location': 'stockLocations',
    };

    // Check if any filters are applied
    const hasFiltersApplied = () => {
        return (
            filters.makes.length > 0 ||
            filters.models.length > 0 ||
            filters.years.length > 0 ||
            filters.categories.length > 0 ||
            filters.brands.length > 0 ||
            filters.materials.length > 0 ||
            filters.warranties.length > 0 ||
            filters.stockLocations.length > 0 ||
            (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 1000) ||
            filters.searchVin !== '' ||
            filters.partType !== '' ||
            filters.includeSuperseded
        );
    };

    // Extract unique values from masterProductList
    useEffect(() => {
        const uniqueMakes = [...new Set(masterProductList.flatMap(p => p.vehicleCompatibility.map(v => v.make)))].filter(Boolean);
        const uniqueModels = [...new Set(masterProductList.flatMap(p => p.vehicleCompatibility.map(v => v.model)))].filter(Boolean);
        const uniqueYears = [...new Set(masterProductList.flatMap(p => p.vehicleCompatibility.map(v => String(v.year))))].filter(Boolean);
        const uniqueCategories = [...new Set(masterProductList.map(p => p.category.en))].filter(Boolean);
        const uniqueBrands = [...new Set(masterProductList.map(p => p.brand.en))].filter(Boolean);
        const uniqueMaterials = [...new Set(masterProductList.map(p => p.material.en))].filter(Boolean);
        const uniqueWarranties = [...new Set(masterProductList.map(p => p.warranty.en))].filter(Boolean);
        const uniqueStockLocations = [...new Set(masterProductList.flatMap(p => p.stock?.locations || []))].filter(Boolean);

        setFilterOptions({
            makes: uniqueMakes,
            models: uniqueModels,
            years: uniqueYears,
            categories: uniqueCategories,
            brands: uniqueBrands,
            materials: uniqueMaterials,
            warranties: uniqueWarranties,
            stockLocations: uniqueStockLocations,
        });

        // Sync initial filters state with no selections
        setFilters(prev => ({
            ...prev,
            ...initialFilters,
        }));
    }, [setFilters]);

    // Add effect to filter products by searchText
    useEffect(() => {
        if (filters.searchText && filters.searchText.length > 0) {
            // Optionally, you can filter the masterProductList here or just let ProductGridView/ProductListView use filters.searchText
            // If you want to update other filters based on searchText, do it here
        }
    }, [filters.searchText]);

    const handleFilterChange = (category, value) => {
        // Map category to correct filters key
        const mappedCategory = typeToKeyMap[category.toLowerCase()] || category;
        if (!filters[mappedCategory]) {
            console.warn(`Invalid filter category: ${mappedCategory}`);
            return;
        }
        setFilters(prev => ({
            ...prev,
            [mappedCategory]: prev[mappedCategory].includes(value)
                ? prev[mappedCategory].filter(v => v !== value)
                : [...prev[mappedCategory], value],
        }));
    };

    const handlePriceChange = (event, newValue) => {
        setFilters(prev => ({ ...prev, priceRange: newValue }));
    };

    // Modern section toggle handler
    const handleSectionToggle = (section) => {
        setExpandedSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    // Modern filter section component
    const FilterSection = ({ title, icon, children, section, count = 0 }) => (
        <Paper
            elevation={0}
            sx={{
                mb: 1,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                overflow: 'hidden',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                    borderColor: 'primary.main',
                    boxShadow: '0 2px 8px rgba(46, 192, 203, 0.15)'
                }
            }}
        >
            <Box
                onClick={() => handleSectionToggle(section)}
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 2,
                    cursor: 'pointer',
                    bgcolor: expandedSections[section] ? 'primary.50' : 'background.paper',
                    transition: 'background-color 0.2s ease',
                    '&:hover': {
                        bgcolor: expandedSections[section] ? 'primary.100' : 'grey.50'
                    }
                }}
            >
                <Stack direction="row" alignItems="center" spacing={1.5}>
                    <Box sx={{ color: 'primary.main', display: 'flex' }}>
                        {icon}
                    </Box>
                    <Typography variant="subtitle2" fontWeight={600} color="text.primary">
                        {title}
                    </Typography>
                    {count > 0 && (
                        <Chip
                            label={count}
                            size="small"
                            sx={{
                                height: 20,
                                fontSize: '0.7rem',
                                bgcolor: 'primary.main',
                                color: 'white',
                                fontWeight: 600
                            }}
                        />
                    )}
                </Stack>
                <ExpandMoreIcon
                    sx={{
                        transform: expandedSections[section] ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'transform 0.2s ease',
                        color: 'text.secondary'
                    }}
                />
            </Box>
            <Collapse in={expandedSections[section]} timeout="auto">
                <Box sx={{ p: 2, pt: 0 }}>
                    {children}
                </Box>
            </Collapse>
        </Paper>
    );

    const filterOptionsBySearch = (options, searchTerm) =>
        options.filter(option => String(option).toLowerCase().includes(searchTerm.toLowerCase()));

    // Get applied filters for display
    const appliedFilters = [
        ...filters.makes.map(make => ({ type: 'Make', value: make })),
        ...filters.models.map(model => ({ type: 'Model', value: model })),
        ...filters.years.map(year => ({ type: 'Year', value: year })),
        ...filters.categories.map(category => ({ type: 'Category', value: category })),
        ...filters.brands.map(brand => ({ type: 'Brand', value: brand })),
        ...filters.materials.map(material => ({ type: 'Material', value: material })),
        ...filters.warranties.map(warranty => ({ type: 'Warranty', value: warranty })),
        ...filters.stockLocations.map(location => ({ type: 'Stock Location', value: location })),
    ].filter(f => f.value);

    const resetFilters = () => {
        setFilters({ ...initialFilters });
        setSearchFilters({
            makes: '',
            models: '',
            years: '',
            categories: '',
            brands: '',
            materials: '',
            warranties: '',
            stockLocations: '',
        });
    };

    return (
        <Box
            className="filters-sidebar"
            sx={{
                p: fullScreen ? { xs: 2, sm: 3 } : { xs: 1, sm: 1.5 },
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                overflowY: fullScreen ? 'hidden' : 'auto',
                '&::-webkit-scrollbar': {
                    width: '6px',
                },
                '&::-webkit-scrollbar-track': {
                    background: '#f1f1f1',
                    borderRadius: '6px',
                },
                '&::-webkit-scrollbar-thumb': {
                    background: 'linear-gradient(135deg, #2EC0CB, #23A3AD)',
                    borderRadius: '6px',
                },
            }}
        >
            {/* Modern Header */}
            {!fullScreen && (
                <Paper
                    elevation={0}
                    sx={{
                        p: 2,
                        mb: 2,
                        bgcolor: 'primary.50',
                        border: '1px solid',
                        borderColor: 'primary.200',
                        borderRadius: 2
                    }}
                >
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <FilterListIcon sx={{ color: 'primary.main', fontSize: 20 }} />
                            <Typography
                                variant="h6"
                                fontWeight={700}
                                sx={{ color: 'primary.main', fontSize: '1rem' }}
                            >
                                Filters
                            </Typography>
                            {appliedFilters.length > 0 && (
                                <Chip
                                    label={appliedFilters.length}
                                    size="small"
                                    sx={{
                                        height: 20,
                                        fontSize: '0.7rem',
                                        bgcolor: 'primary.main',
                                        color: 'white',
                                        fontWeight: 600
                                    }}
                                />
                            )}
                        </Stack>
                        <Stack direction="row" spacing={0.5}>
                            <Tooltip title="Reset All Filters" arrow>
                                <IconButton
                                    size="small"
                                    onClick={resetFilters}
                                    disabled={appliedFilters.length === 0}
                                    sx={{
                                        color: appliedFilters.length > 0 ? 'error.main' : 'text.disabled',
                                        '&:hover': {
                                            bgcolor: appliedFilters.length > 0 ? 'error.50' : 'transparent'
                                        }
                                    }}
                                >
                                    <ClearIcon fontSize="small" />
                                </IconButton>
                            </Tooltip>
                        </Stack>
                    </Stack>
                </Paper>
            )}

            {/* Active Filters */}
            {appliedFilters.length > 0 && (
                <Paper
                    elevation={0}
                    sx={{
                        p: 2,
                        mb: 2,
                        bgcolor: 'grey.50',
                        border: '1px solid',
                        borderColor: 'grey.200',
                        borderRadius: 2
                    }}
                >
                    <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
                        <Typography variant="subtitle2" fontWeight={600} color="text.primary">
                            Active Filters ({appliedFilters.length})
                        </Typography>
                        <Button
                            size="small"
                            onClick={resetFilters}
                            startIcon={<ClearIcon />}
                            sx={{
                                fontSize: '0.75rem',
                                color: 'error.main',
                                '&:hover': { bgcolor: 'error.50' }
                            }}
                        >
                            Clear All
                        </Button>
                    </Stack>
                    <Stack direction="row" flexWrap="wrap" gap={0.5}>
                        {appliedFilters.map((filter, index) => (
                            <Chip
                                key={index}
                                label={`${filter.type}: ${filter.value}`}
                                size="small"
                                onDelete={() => handleFilterChange(filter.type.toLowerCase().replace(' ', ''), filter.value)}
                                sx={{
                                    fontSize: '0.75rem',
                                    height: 24,
                                    bgcolor: 'primary.100',
                                    color: 'primary.dark',
                                    '& .MuiChip-deleteIcon': {
                                        color: 'primary.main',
                                        fontSize: '16px',
                                        '&:hover': { color: 'error.main' }
                                    },
                                }}
                            />
                        ))}
                    </Stack>
                </Paper>
            )}

            {/* Quick Actions */}
            <Paper
                elevation={0}
                sx={{
                    p: 1.5,
                    mb: 2,
                    bgcolor: 'background.paper',
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2
                }}
            >
                <Typography variant="subtitle2" fontWeight={600} mb={1.5} color="text.primary">
                    Quick Search
                </Typography>
                <Stack spacing={1}>
                    <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setImageSearchOpen(true)}
                        startIcon={<CameraAltIcon />}
                        sx={{
                            justifyContent: 'flex-start',
                            fontSize: '0.8rem',
                            py: 1,
                            borderColor: 'primary.main',
                            color: 'primary.main',
                            '&:hover': {
                                borderColor: 'primary.dark',
                                bgcolor: 'primary.50'
                            },
                        }}
                    >
                        Image Search
                    </Button>
                    <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setBarcodeScannerOpen(true)}
                        startIcon={<MicIcon />}
                        sx={{
                            justifyContent: 'flex-start',
                            fontSize: '0.8rem',
                            py: 1,
                            borderColor: 'primary.main',
                            color: 'primary.main',
                            '&:hover': {
                                borderColor: 'primary.dark',
                                bgcolor: 'primary.50'
                            },
                        }}
                    >
                        Barcode Scanner
                    </Button>
                    <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setBulkOrderOpen(true)}
                        startIcon={<ShoppingCartIcon />}
                        sx={{
                            justifyContent: 'flex-start',
                            fontSize: '0.8rem',
                            py: 1,
                            borderColor: 'primary.main',
                            color: 'primary.main',
                            '&:hover': {
                                borderColor: 'primary.dark',
                                bgcolor: 'primary.50'
                            },
                        }}
                    >
                        Bulk Order
                    </Button>
                </Stack>
            </Paper>

            {/* Modern Filter Sections */}
            <Box sx={{ flex: 1, overflow: 'auto' }}>
                {/* Vehicle Information */}
                <FilterSection
                    title="Vehicle Information"
                    icon={<DirectionsCarIcon />}
                    section="vehicle"
                    count={filters.makes.length + filters.models.length + filters.years.length}
                >
                    <Stack spacing={2}>
                        {/* Makes */}
                        <Box>
                            <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
                                Makes
                            </Typography>
                            <Autocomplete
                                multiple
                                options={filterOptions.makes}
                                value={filters.makes}
                                onChange={(event, newValue) => {
                                    setFilters(prev => ({ ...prev, makes: newValue }));
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder="Select makes..."
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                renderTags={(value, getTagProps) =>
                                    value.map((option, index) => (
                                        <Chip
                                            variant="outlined"
                                            label={option}
                                            size="small"
                                            {...getTagProps({ index })}
                                            key={index}
                                        />
                                    ))
                                }
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 2
                                    }
                                }}
                            />
                        </Box>

                        {/* Models */}
                        <Box>
                            <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
                                Models
                            </Typography>
                            <Autocomplete
                                multiple
                                options={filterOptions.models}
                                value={filters.models}
                                onChange={(event, newValue) => {
                                    setFilters(prev => ({ ...prev, models: newValue }));
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder="Select models..."
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                renderTags={(value, getTagProps) =>
                                    value.map((option, index) => (
                                        <Chip
                                            variant="outlined"
                                            label={option}
                                            size="small"
                                            {...getTagProps({ index })}
                                            key={index}
                                        />
                                    ))
                                }
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 2
                                    }
                                }}
                            />
                        </Box>

                        {/* Years */}
                        <Box>
                            <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
                                Years
                            </Typography>
                            <Autocomplete
                                multiple
                                options={filterOptions.years}
                                value={filters.years}
                                onChange={(event, newValue) => {
                                    setFilters(prev => ({ ...prev, years: newValue }));
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder="Select years..."
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                renderTags={(value, getTagProps) =>
                                    value.map((option, index) => (
                                        <Chip
                                            variant="outlined"
                                            label={option}
                                            size="small"
                                            {...getTagProps({ index })}
                                            key={index}
                                        />
                                    ))
                                }
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 2
                                    }
                                }}
                            />
                        </Box>
                    </Stack>
                </FilterSection>

                {/* Categories & Brands */}
                <FilterSection
                    title="Categories & Brands"
                    icon={<CategoryIcon />}
                    section="category"
                    count={filters.categories.length + filters.brands.length}
                >
                    <Stack spacing={2}>
                        {/* Categories */}
                        <Box>
                            <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
                                Categories
                            </Typography>
                            <Autocomplete
                                multiple
                                options={filterOptions.categories}
                                value={filters.categories}
                                onChange={(event, newValue) => {
                                    setFilters(prev => ({ ...prev, categories: newValue }));
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder="Select categories..."
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 2
                                    }
                                }}
                            />
                        </Box>

                        {/* Brands */}
                        <Box>
                            <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
                                Brands
                            </Typography>
                            <Autocomplete
                                multiple
                                options={filterOptions.brands}
                                value={filters.brands}
                                onChange={(event, newValue) => {
                                    setFilters(prev => ({ ...prev, brands: newValue }));
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder="Select brands..."
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 2
                                    }
                                }}
                            />
                        </Box>
                    </Stack>
                </FilterSection>

                {/* Price Range */}
                <FilterSection
                    title="Price Range"
                    icon={<AttachMoneyIcon />}
                    section="price"
                    count={filters.priceRange[0] !== 0 || filters.priceRange[1] !== 10000 ? 1 : 0}
                >
                    <Box>
                        <Typography variant="body2" fontWeight={600} mb={2} color="text.primary">
                            Price: ${filters.priceRange[0]} - ${filters.priceRange[1]}
                        </Typography>
                        <Slider
                            value={filters.priceRange}
                            onChange={handlePriceChange}
                            valueLabelDisplay="auto"
                            min={0}
                            max={10000}
                            step={100}
                            sx={{
                                color: 'primary.main',
                                '& .MuiSlider-thumb': {
                                    width: 20,
                                    height: 20,
                                    '&:hover': {
                                        boxShadow: '0 0 0 8px rgba(46, 192, 203, 0.16)'
                                    }
                                },
                                '& .MuiSlider-track': {
                                    height: 6,
                                    borderRadius: 3
                                },
                                '& .MuiSlider-rail': {
                                    height: 6,
                                    borderRadius: 3,
                                    bgcolor: 'grey.200'
                                }
                            }}
                        />
                        <Stack direction="row" justifyContent="space-between" mt={1}>
                            <Typography variant="caption" color="text.secondary">$0</Typography>
                            <Typography variant="caption" color="text.secondary">$10,000+</Typography>
                        </Stack>
                    </Box>
                </FilterSection>

                {/* Additional Features */}
                <FilterSection
                    title="Features & Location"
                    icon={<LocationOnIcon />}
                    section="features"
                    count={filters.materials.length + filters.warranties.length + filters.stockLocations.length}
                >
                    <Stack spacing={2}>
                        {/* Materials */}
                        <Box>
                            <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
                                Materials
                            </Typography>
                            <Autocomplete
                                multiple
                                options={filterOptions.materials}
                                value={filters.materials}
                                onChange={(event, newValue) => {
                                    setFilters(prev => ({ ...prev, materials: newValue }));
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder="Select materials..."
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 2
                                    }
                                }}
                            />
                        </Box>

                        {/* Stock Locations */}
                        <Box>
                            <Typography variant="body2" fontWeight={600} mb={1} color="text.primary">
                                Stock Locations
                            </Typography>
                            <Autocomplete
                                multiple
                                options={filterOptions.stockLocations}
                                value={filters.stockLocations}
                                onChange={(event, newValue) => {
                                    setFilters(prev => ({ ...prev, stockLocations: newValue }));
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder="Select locations..."
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 2
                                    }
                                }}
                            />
                        </Box>
                    </Stack>
                </FilterSection>
            </Box>

            {/* Full Screen Modern Layout */}
            {fullScreen && (
                <Box sx={{ flex: 1, overflow: 'auto', px: 1 }}>
                    <Grid container spacing={3} sx={{ height: '100%' }}>
                        {/* VIN Search */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        VIN Search
                                    </Typography>
                                    <TextField
                                        placeholder="Enter 17-digit VIN"
                                        size="medium"
                                        fullWidth
                                        value={filters.searchVin}
                                        onChange={(e) => setFilters(prev => ({ ...prev, searchVin: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Vehicle Make */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Vehicle Make
                                    </Typography>
                                    <TextField
                                        placeholder="Search Makes..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.makes}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, makes: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.makes, searchFilters.makes).slice(0, 8).map(make => (
                                            <FormControlLabel
                                                key={make}
                                                control={
                                                    <Checkbox
                                                        checked={filters.makes.includes(make)}
                                                        onChange={() => handleFilterChange('makes', make)}
                                                        size="small"
                                                    />
                                                }
                                                label={make}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Vehicle Model */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Vehicle Model
                                    </Typography>
                                    <TextField
                                        placeholder="Search Models..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.models}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, models: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.models, searchFilters.models).slice(0, 8).map(model => (
                                            <FormControlLabel
                                                key={model}
                                                control={
                                                    <Checkbox
                                                        checked={filters.models.includes(model)}
                                                        onChange={() => handleFilterChange('models', model)}
                                                        size="small"
                                                    />
                                                }
                                                label={model}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Categories */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Categories
                                    </Typography>
                                    <TextField
                                        placeholder="Search Categories..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.categories}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, categories: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.categories, searchFilters.categories).slice(0, 8).map(category => (
                                            <FormControlLabel
                                                key={category}
                                                control={
                                                    <Checkbox
                                                        checked={filters.categories.includes(category)}
                                                        onChange={() => handleFilterChange('categories', category)}
                                                        size="small"
                                                    />
                                                }
                                                label={category}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Brands */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Brands
                                    </Typography>
                                    <TextField
                                        placeholder="Search Brands..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.brands}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, brands: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.brands, searchFilters.brands).slice(0, 8).map(brand => (
                                            <FormControlLabel
                                                key={brand}
                                                control={
                                                    <Checkbox
                                                        checked={filters.brands.includes(brand)}
                                                        onChange={() => handleFilterChange('brands', brand)}
                                                        size="small"
                                                    />
                                                }
                                                label={brand}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Price Range */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Price Range
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary" mb={2}>
                                        ${filters.priceRange[0]} - ${filters.priceRange[1]}
                                    </Typography>
                                    <Slider
                                        value={filters.priceRange}
                                        onChange={handlePriceChange}
                                        valueLabelDisplay="auto"
                                        min={0}
                                        max={1000}
                                        sx={{ mt: 2 }}
                                    />
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>
                </Box>
            ) : (
                // Original sidebar layout
                <Box sx={{ flex: 1, overflow: 'auto' }}>
                    <Box mb={3}>
                <Typography variant="subtitle2" fontWeight={500} mb={1.5} color="text.primary" sx={{ fontSize: '0.85rem', px: 0.5 }}>
                    VIN Search
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        id="vin-search"
                        placeholder="Enter 17-digit VIN"
                        size="small"
                        fullWidth
                        value={filters.searchVin}
                        onChange={(e) => setFilters(prev => ({ ...prev, searchVin: e.target.value }))}
                        sx={{
                            fontSize: '0.8rem',
                            '& .MuiOutlinedInput-root': {
                                height: 36,
                                fontSize: '0.8rem'
                            }
                        }}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                </InputAdornment>
                            ),
                        }}
                    />
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" fontWeight={500} mb={1} color="text.primary" sx={{ fontSize: '0.85rem', px: 0.5 }}>
                    Vehicle Make
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Makes..."
                        size="small"
                        fullWidth
                        value={searchFilters.makes}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, makes: e.target.value }))}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                </InputAdornment>
                            ),
                        }}
                        sx={{
                            mb: 1,
                            fontSize: '0.8rem',
                            '& .MuiOutlinedInput-root': {
                                height: 36,
                                fontSize: '0.8rem'
                            }
                        }}
                    />
                    <Box sx={{ maxHeight: 120, overflowY: 'auto', mb: 1 }}>
                        {filterOptionsBySearch(filterOptions.makes, searchFilters.makes).map(make => (
                            <FormControlLabel
                                key={make}
                                control={
                                    <Checkbox
                                        checked={filters.makes.includes(make)}
                                        onChange={() => handleFilterChange('makes', make)}
                                        size="small"
                                        sx={{ py: 0.25 }}
                                    />
                                }
                                label={make}
                                sx={{
                                    display: 'flex',
                                    fontSize: '0.8rem',
                                    '& .MuiTypography-root': {
                                        color: 'text.secondary',
                                        fontSize: '0.8rem'
                                    },
                                    '& .MuiFormControlLabel-root': {
                                        marginBottom: 0
                                    }
                                }}
                            />
                        ))}
                    </Box>
                </Box>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Vehicle Model
                </Typography>
                <TextField
                    placeholder="Search Models..."
                    size="small"
                    fullWidth
                    value={searchFilters.models}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, models: e.target.value }))}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <SearchIcon sx={{ color: '#666' }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                />
                <Box sx={{ maxHeight: 150, overflowY: 'auto', mb: 2 }}>
                    {filterOptionsBySearch(filterOptions.models, searchFilters.models).map(model => (
                        <FormControlLabel
                            key={model}
                            control={<Checkbox checked={filters.models.includes(model)} onChange={() => handleFilterChange('models', model)} />}
                            label={model}
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                        />
                    ))}
                </Box>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Vehicle Year
                </Typography>
                <TextField
                    placeholder="Search Years..."
                    size="small"
                    fullWidth
                    value={searchFilters.years}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, years: e.target.value }))}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <SearchIcon sx={{ color: '#666' }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                />
                <Box sx={{ maxHeight: 150, overflowY: 'auto', mb: 2 }}>
                    {filterOptionsBySearch(filterOptions.years, searchFilters.years).map(year => (
                        <FormControlLabel
                            key={year}
                            control={<Checkbox checked={filters.years.includes(year)} onChange={() => handleFilterChange('years', year)} />}
                            label={year}
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                        />
                    ))}
                </Box>
            </Box>

            <Divider sx={{ my: 3, borderColor: '#E0E0E0' }} />

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Category
                </Typography>
                <TextField
                    placeholder="Search Categories..."
                    size="small"
                    fullWidth
                    value={searchFilters.categories}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, categories: e.target.value }))}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <SearchIcon sx={{ color: '#666' }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                />
                <Box sx={{ maxHeight: 150, overflowY: 'auto', mb: 2 }}>
                    {filterOptionsBySearch(filterOptions.categories, searchFilters.categories).map(category => (
                        <FormControlLabel
                            key={category}
                            control={<Checkbox checked={filters.categories.includes(category)} onChange={() => handleFilterChange('categories', category)} />}
                            label={category}
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                        />
                    ))}
                </Box>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Brand
                </Typography>
                <TextField
                    placeholder="Search Brands..."
                    size="small"
                    fullWidth
                    value={searchFilters.brands}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, brands: e.target.value }))}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <SearchIcon sx={{ color: '#666' }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                />
                <Box sx={{ maxHeight: 150, overflowY: 'auto', mb: 2 }}>
                    {filterOptionsBySearch(filterOptions.brands, searchFilters.brands).map(brand => (
                        <FormControlLabel
                            key={brand}
                            control={<Checkbox checked={filters.brands.includes(brand)} onChange={() => handleFilterChange('brands', brand)} />}
                            label={brand}
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                        />
                    ))}
                </Box>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Price Range
                </Typography>
                <Slider
                    value={filters.priceRange}
                    onChange={handlePriceChange}
                    valueLabelDisplay="auto"
                    min={0}
                    max={1000}
                    step={10}
                    sx={{ color: '#2EC0CB', '& .MuiSlider-thumb': { '&:hover, &.Mui-focusVisible': { boxShadow: '0 0 0 8px rgba(46, 192, 203, 0.16)' } } }}
                />
                <Typography
                    textAlign="center"
                    variant="body2"
                    sx={{ fontSize: { xs: '0.7rem', sm: '0.875rem' }, color: '#666', mt: 1 }}
                >
                    Price: ${filters.priceRange[0]} - ${filters.priceRange[1]}
                </Typography>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Material
                </Typography>
                <TextField
                    placeholder="Search Materials..."
                    size="small"
                    fullWidth
                    value={searchFilters.materials}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, materials: e.target.value }))}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <SearchIcon sx={{ color: '#666' }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                />
                <Box sx={{ maxHeight: 150, overflowY: 'auto', mb: 2 }}>
                    {filterOptionsBySearch(filterOptions.materials, searchFilters.materials).map(material => (
                        <FormControlLabel
                            key={material}
                            control={<Checkbox checked={filters.materials.includes(material)} onChange={() => handleFilterChange('materials', material)} />}
                            label={material}
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                        />
                    ))}
                </Box>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Warranty
                </Typography>
                <TextField
                    placeholder="Search Warranties..."
                    size="small"
                    fullWidth
                    value={searchFilters.warranties}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, warranties: e.target.value }))}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <SearchIcon sx={{ color: '#666' }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                />
                <Box sx={{ maxHeight: 150, overflowY: 'auto', mb: 2 }}>
                    {filterOptionsBySearch(filterOptions.warranties, searchFilters.warranties).map(warranty => (
                        <FormControlLabel
                            key={warranty}
                            control={<Checkbox checked={filters.warranties.includes(warranty)} onChange={() => handleFilterChange('warranties', warranty)} />}
                            label={warranty}
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                        />
                    ))}
                </Box>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Stock Location
                </Typography>
                <TextField
                    placeholder="Search Locations..."
                    size="small"
                    fullWidth
                    value={searchFilters.stockLocations}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, stockLocations: e.target.value }))}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <SearchIcon sx={{ color: '#666' }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                />
                <Box sx={{ maxHeight: 150, overflowY: 'auto', mb: 2 }}>
                    {filterOptionsBySearch(filterOptions.stockLocations, searchFilters.stockLocations).map(location => (
                        <FormControlLabel
                            key={location}
                            control={<Checkbox checked={filters.stockLocations.includes(location)} onChange={() => handleFilterChange('stockLocations', location)} />}
                            label={location}
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                        />
                    ))}
                </Box>
            </Box>

            <Divider sx={{ my: 3, borderColor: '#E0E0E0' }} />

            <Box mb={4}>
                <Typography variant="subtitle1" fontWeight={500} mb={2} color="#333" fontSize={{ xs: '0.9rem', sm: '1rem' }}>
                    Part Type
                </Typography>
                <FormControlLabel
                    control={<Checkbox checked={filters.partType === 'primary'} onChange={(e) => setFilters(prev => ({ ...prev, partType: e.target.checked ? 'primary' : '' }))} />}
                    label="Primary Parts"
                    sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                />
                <FormControlLabel
                    control={<Checkbox checked={filters.includeSuperseded} onChange={(e) => setFilters(prev => ({ ...prev, includeSuperseded: e.target.checked }))} />}
                    label="Include Superseded"
                    sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, '& .MuiTypography-root': { color: '#555' } }}
                />
            </Box>
                </Box>
            )}
        </Box>
    );
}

export default FiltersSidebar;