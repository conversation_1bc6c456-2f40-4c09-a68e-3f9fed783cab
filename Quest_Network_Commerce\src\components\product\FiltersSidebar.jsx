import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    Typo<PERSON>,
    TextField,
    Button,
    FormControlLabel,
    Checkbox,
    Slider,
    Divider,
    InputAdornment,
    IconButton,
    Chip,
    Stack,
    Tooltip,
    Grid,
    Card,
    CardContent
} from '@mui/material';
import {
    Search as SearchIcon,
    Save as SaveIcon,
    RestartAlt as RestartAltIcon,
} from '@mui/icons-material';
import { masterProductList } from '../../data/masterProductList';

function FiltersSidebar({ filters, setFilters, fullScreen = false }) {
    const [searchFilters, setSearchFilters] = useState({
        makes: '',
        models: '',
        years: '',
        categories: '',
        brands: '',
        materials: '',
        warranties: '',
        stockLocations: '',
        partTypes: '',
    });

    // Consistent styling for all form elements
    const inputStyles = {
        '& .MuiOutlinedInput-root': {
            fontSize: '0.8rem',
            '& fieldset': { borderColor: 'rgba(46, 192, 203, 0.3)' },
            '&:hover fieldset': { borderColor: 'rgba(46, 192, 203, 0.5)' },
            '&.Mui-focused fieldset': { borderColor: 'primary.main' }
        }
    };

    const checkboxStyles = {
        color: 'rgba(46, 192, 203, 0.6)',
        '&.Mui-checked': { color: 'primary.main' },
        '& .MuiSvgIcon-root': { fontSize: 18 }
    };

    const labelStyles = {
        fontSize: '0.8rem',
        '& .MuiTypography-root': {
            color: 'text.secondary',
            fontSize: '0.8rem'
        }
    };

    const sectionTitleStyles = {
        fontSize: '0.85rem',
        fontWeight: 500,
        color: 'text.primary',
        px: 0.5
    };

    // State to store unique options dynamically
    const [filterOptions, setFilterOptions] = useState({
        makes: [],
        models: [],
        years: [],
        categories: [],
        brands: [],
        materials: [],
        warranties: [],
        stockLocations: [],
    });

    // Initial filter state for comparison
    const initialFilters = {
        makes: [],
        models: [],
        years: [],
        categories: [],
        brands: [],
        materials: [],
        warranties: [],
        stockLocations: [],
        partTypes: [],
        priceRange: [0, 1000],
        searchVin: '',
        includeSuperseded: false,
    };

    // Mapping of chip type to filters key
    const typeToKeyMap = {
        make: 'makes',
        model: 'models',
        year: 'years',
        category: 'categories',
        brand: 'brands',
        material: 'materials',
        warranty: 'warranties',
        'stock location': 'stockLocations',
        'part type': 'partTypes',
    };

    // Check if any filters are applied
    const hasFiltersApplied = () => {
        return (
            filters.makes.length > 0 ||
            filters.models.length > 0 ||
            filters.years.length > 0 ||
            filters.categories.length > 0 ||
            filters.brands.length > 0 ||
            filters.materials.length > 0 ||
            filters.warranties.length > 0 ||
            filters.stockLocations.length > 0 ||
            filters.partTypes.length > 0 ||
            (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 1000) ||
            filters.searchVin !== '' ||
            filters.includeSuperseded
        );
    };

    // Extract unique values from masterProductList
    useEffect(() => {
        const uniqueMakes = [...new Set(masterProductList.flatMap(p => p.vehicleCompatibility.map(v => v.make)))].filter(Boolean);
        const uniqueModels = [...new Set(masterProductList.flatMap(p => p.vehicleCompatibility.map(v => v.model)))].filter(Boolean);
        const uniqueYears = [...new Set(masterProductList.flatMap(p => p.vehicleCompatibility.map(v => String(v.year))))].filter(Boolean);
        const uniqueCategories = [...new Set(masterProductList.map(p => p.category.en))].filter(Boolean);
        const uniqueBrands = [...new Set(masterProductList.map(p => p.brand.en))].filter(Boolean);
        const uniqueMaterials = [...new Set(masterProductList.map(p => p.material.en))].filter(Boolean);
        const uniqueWarranties = [...new Set(masterProductList.map(p => p.warranty.en))].filter(Boolean);
        const uniqueStockLocations = [...new Set(masterProductList.flatMap(p => p.stock?.locations || []))].filter(Boolean);
        const uniquePartTypes = [...new Set(masterProductList.map(p => p.type))].filter(Boolean);

        setFilterOptions({
            makes: uniqueMakes,
            models: uniqueModels,
            years: uniqueYears,
            categories: uniqueCategories,
            brands: uniqueBrands,
            materials: uniqueMaterials,
            warranties: uniqueWarranties,
            stockLocations: uniqueStockLocations,
            partTypes: uniquePartTypes,
        });

        // Sync initial filters state with no selections
        setFilters(prev => ({
            ...prev,
            ...initialFilters,
        }));
    }, [setFilters]);

    // Add effect to filter products by searchText
    useEffect(() => {
        if (filters.searchText && filters.searchText.length > 0) {
            // Optionally, you can filter the masterProductList here or just let ProductGridView/ProductListView use filters.searchText
            // If you want to update other filters based on searchText, do it here
        }
    }, [filters.searchText]);

    const handleFilterChange = (category, value) => {
        // Map category to correct filters key
        const mappedCategory = typeToKeyMap[category.toLowerCase()] || category;
        if (!filters[mappedCategory]) {
            console.warn(`Invalid filter category: ${mappedCategory}`);
            return;
        }
        setFilters(prev => ({
            ...prev,
            [mappedCategory]: prev[mappedCategory].includes(value)
                ? prev[mappedCategory].filter(v => v !== value)
                : [...prev[mappedCategory], value],
        }));
    };

    const handlePriceChange = (event, newValue) => {
        setFilters(prev => ({ ...prev, priceRange: newValue }));
    };

    const filterOptionsBySearch = (options, searchTerm) =>
        options.filter(option => String(option).toLowerCase().includes(searchTerm.toLowerCase()));

    // Get applied filters for display
    const appliedFilters = [
        ...filters.makes.map(make => ({ type: 'Make', value: make })),
        ...filters.models.map(model => ({ type: 'Model', value: model })),
        ...filters.years.map(year => ({ type: 'Year', value: year })),
        ...filters.categories.map(category => ({ type: 'Category', value: category })),
        ...filters.brands.map(brand => ({ type: 'Brand', value: brand })),
        ...filters.materials.map(material => ({ type: 'Material', value: material })),
        ...filters.warranties.map(warranty => ({ type: 'Warranty', value: warranty })),
        ...filters.stockLocations.map(location => ({ type: 'Stock Location', value: location })),
        ...filters.partTypes.map(partType => ({ type: 'Part Type', value: partType })),
    ].filter(f => f.value);

    const resetFilters = () => {
        setFilters({ ...initialFilters });
        setSearchFilters({
            makes: '',
            models: '',
            years: '',
            categories: '',
            brands: '',
            materials: '',
            warranties: '',
            stockLocations: '',
        });
    };

    return (
        <Box
            className="filters-sidebar"
            sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative'
            }}
        >
            {/* Scrollable Content Container */}
            <Box
                sx={{
                    flex: 1,
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    p: fullScreen ? { xs: 2, sm: 3 } : { xs: 2, sm: 2.5 },
                    '&::-webkit-scrollbar': {
                        width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                        background: 'rgba(46, 192, 203, 0.05)',
                        borderRadius: '8px',
                        margin: '8px 0',
                    },
                    '&::-webkit-scrollbar-thumb': {
                        background: 'linear-gradient(135deg, #2EC0CB, #23A3AD)',
                        borderRadius: '8px',
                        '&:hover': {
                            background: 'linear-gradient(135deg, #23A3AD, #1E8B95)',
                        }
                    },
                }}
            >
            {/* Modern Header - Only show in sidebar mode */}
            {!fullScreen && (
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 3,
                    pb: 2,
                    borderBottom: '1px solid rgba(46, 192, 203, 0.1)'
                }}>
                    <Box>
                        <Typography
                            variant="h6"
                            fontWeight={700}
                            sx={{
                                fontSize: '1.1rem',
                                color: 'text.primary',
                                mb: 0.5
                            }}
                        >
                            Filters
                        </Typography>
                        <Typography
                            variant="caption"
                            sx={{
                                color: 'text.secondary',
                                fontSize: '0.75rem'
                            }}
                        >
                            Refine your search
                        </Typography>
                    </Box>
                    <Stack direction="row" spacing={1}>
                        <Tooltip title="Save Filters" arrow>
                            <IconButton
                                size="small"
                                onClick={() => console.log('Filters saved:', filters)}
                                sx={{
                                    color: 'primary.main',
                                    width: 32,
                                    height: 32,
                                    bgcolor: 'rgba(46, 192, 203, 0.08)',
                                    '&:hover': {
                                        bgcolor: 'rgba(46, 192, 203, 0.15)',
                                        transform: 'scale(1.05)'
                                    },
                                    transition: 'all 0.2s ease'
                                }}
                            >
                                <SaveIcon fontSize="small" />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="Reset All Filters" arrow>
                            <IconButton
                                size="small"
                                onClick={resetFilters}
                                sx={{
                                    color: 'text.secondary',
                                    width: 32,
                                    height: 32,
                                    bgcolor: 'rgba(0, 0, 0, 0.04)',
                                    '&:hover': {
                                        bgcolor: 'rgba(0, 0, 0, 0.08)',
                                        transform: 'scale(1.05)'
                                    },
                                    transition: 'all 0.2s ease'
                                }}
                            >
                                <RestartAltIcon fontSize="small" />
                            </IconButton>
                        </Tooltip>
                    </Stack>
                </Box>
            )}

            {appliedFilters.length > 0 && (
                <Box mb={3}>
                    <Typography
                        variant="subtitle2"
                        fontWeight={600}
                        mb={1.5}
                        color="text.primary"
                        sx={{
                            fontSize: '0.875rem',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                        }}
                    >
                        Active Filters
                        <Chip
                            label={appliedFilters.length}
                            size="small"
                            sx={{
                                height: 20,
                                minWidth: 20,
                                fontSize: '0.7rem',
                                bgcolor: 'primary.main',
                                color: 'white',
                                fontWeight: 600
                            }}
                        />
                    </Typography>
                    <Stack direction="row" flexWrap="wrap" gap={1}>
                        {appliedFilters.slice(0, 6).map((filter, index) => (
                            <Chip
                                key={index}
                                label={filter.value}
                                size="small"
                                onDelete={() => handleFilterChange(filter.type.toLowerCase(), filter.value)}
                                sx={{
                                    fontSize: '0.75rem',
                                    height: 28,
                                    bgcolor: 'rgba(46, 192, 203, 0.1)',
                                    color: 'primary.main',
                                    border: '1px solid rgba(46, 192, 203, 0.2)',
                                    '& .MuiChip-deleteIcon': {
                                        color: 'primary.main',
                                        fontSize: '16px',
                                        '&:hover': {
                                            color: 'primary.dark'
                                        }
                                    },
                                    '&:hover': {
                                        bgcolor: 'rgba(46, 192, 203, 0.15)',
                                        borderColor: 'rgba(46, 192, 203, 0.3)'
                                    },
                                    transition: 'all 0.2s ease'
                                }}
                            />
                        ))}
                        {appliedFilters.length > 6 && (
                            <Chip
                                label={`+${appliedFilters.length - 6} more`}
                                size="small"
                                sx={{
                                    fontSize: '0.75rem',
                                    height: 28,
                                    bgcolor: 'rgba(46, 192, 203, 0.08)',
                                    color: 'primary.main',
                                    border: '1px solid rgba(46, 192, 203, 0.15)',
                                }}
                            />
                        )}
                    </Stack>
                </Box>
            )}



            <Divider sx={{ my: 1.5, borderColor: 'rgba(46, 192, 203, 0.2)' }} />

            {/* Full Screen Grid Layout */}
            {fullScreen ? (
                <Box sx={{ flex: 1, overflow: 'auto', px: 1 }}>
                    <Grid container spacing={3} sx={{ height: '100%' }}>
                        {/* VIN Search */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        VIN Search
                                    </Typography>
                                    <TextField
                                        placeholder="Enter 17-digit VIN"
                                        size="medium"
                                        fullWidth
                                        value={filters.searchVin}
                                        onChange={(e) => setFilters(prev => ({ ...prev, searchVin: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Vehicle Make */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Asset Make
                                    </Typography>
                                    <TextField
                                        placeholder="Search Makes..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.makes}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, makes: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.makes, searchFilters.makes).slice(0, 8).map(make => (
                                            <FormControlLabel
                                                key={make}
                                                control={
                                                    <Checkbox
                                                        checked={filters.makes.includes(make)}
                                                        onChange={() => handleFilterChange('makes', make)}
                                                        size="small"
                                                    />
                                                }
                                                label={make}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Vehicle Model */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Vehicle Model
                                    </Typography>
                                    <TextField
                                        placeholder="Search Models..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.models}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, models: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.models, searchFilters.models).slice(0, 8).map(model => (
                                            <FormControlLabel
                                                key={model}
                                                control={
                                                    <Checkbox
                                                        checked={filters.models.includes(model)}
                                                        onChange={() => handleFilterChange('models', model)}
                                                        size="small"
                                                    />
                                                }
                                                label={model}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Categories */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Categories
                                    </Typography>
                                    <TextField
                                        placeholder="Search Categories..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.categories}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, categories: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.categories, searchFilters.categories).slice(0, 8).map(category => (
                                            <FormControlLabel
                                                key={category}
                                                control={
                                                    <Checkbox
                                                        checked={filters.categories.includes(category)}
                                                        onChange={() => handleFilterChange('categories', category)}
                                                        size="small"
                                                    />
                                                }
                                                label={category}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Brands */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Brands
                                    </Typography>
                                    <TextField
                                        placeholder="Search Brands..."
                                        size="medium"
                                        fullWidth
                                        value={searchFilters.brands}
                                        onChange={(e) => setSearchFilters(prev => ({ ...prev, brands: e.target.value }))}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                                        {filterOptionsBySearch(filterOptions.brands, searchFilters.brands).slice(0, 8).map(brand => (
                                            <FormControlLabel
                                                key={brand}
                                                control={
                                                    <Checkbox
                                                        checked={filters.brands.includes(brand)}
                                                        onChange={() => handleFilterChange('brands', brand)}
                                                        size="small"
                                                    />
                                                }
                                                label={brand}
                                                sx={{ display: 'block', mb: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        {/* Price Range */}
                        <Grid item xs={12} md={6} lg={4}>
                            <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                                <CardContent sx={{ p: 2 }}>
                                    <Typography variant="h6" fontWeight={600} mb={2} color="primary.main">
                                        Price Range
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary" mb={2}>
                                        ${filters.priceRange[0]} - ${filters.priceRange[1]}
                                    </Typography>
                                    <Slider
                                        value={filters.priceRange}
                                        onChange={handlePriceChange}
                                        valueLabelDisplay="auto"
                                        min={0}
                                        max={1000}
                                        sx={{ mt: 2 }}
                                    />
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>
                </Box>
            ) : (
                // Original sidebar layout
                <Box sx={{ flex: 1, overflow: 'auto' }}>
                    <Box mb={3}>
                <Typography variant="subtitle2" fontWeight={500} mb={1.5} color="text.primary" sx={{ fontSize: '0.85rem', px: 0.5 }}>
                    VIN Search
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        id="vin-search"
                        placeholder="Enter 17-digit VIN"
                        size="small"
                        fullWidth
                        value={filters.searchVin}
                        onChange={(e) => setFilters(prev => ({ ...prev, searchVin: e.target.value }))}
                        sx={{
                            fontSize: '0.8rem',
                            '& .MuiOutlinedInput-root': {
                                height: 36,
                                fontSize: '0.8rem'
                            }
                        }}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                </InputAdornment>
                            ),
                        }}
                    />
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Asset Make
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Makes..."
                        size="small"
                        fullWidth
                        value={searchFilters.makes}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, makes: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Box sx={{ maxHeight: 120, overflowY: 'auto', mb: 1 }}>
                        {filterOptionsBySearch(filterOptions.makes, searchFilters.makes).map(make => (
                            <FormControlLabel
                                key={make}
                                control={
                                    <Checkbox
                                        checked={filters.makes.includes(make)}
                                        onChange={() => handleFilterChange('makes', make)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={make}
                                sx={labelStyles}
                            />
                        ))}
                    </Box>
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Asset Model
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Models..."
                        size="small"
                        fullWidth
                        value={searchFilters.models}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, models: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Stack spacing={0.5} maxHeight={120} overflow="auto">
                        {filterOptionsBySearch(filterOptions.models, searchFilters.models).map(model => (
                            <FormControlLabel
                                key={model}
                                control={
                                    <Checkbox
                                        checked={filters.models.includes(model)}
                                        onChange={() => handleFilterChange('models', model)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={model}
                                sx={labelStyles}
                            />
                        ))}
                    </Stack>
                </Box>
            </Box>

            <Box mb={4}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Asset Year
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Years..."
                        size="small"
                        fullWidth
                        value={searchFilters.years}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, years: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Stack spacing={0.5} maxHeight={120} overflow="auto">
                        {filterOptionsBySearch(filterOptions.years, searchFilters.years).map(year => (
                            <FormControlLabel
                                key={year}
                                control={
                                    <Checkbox
                                        checked={filters.years.includes(year)}
                                        onChange={() => handleFilterChange('years', year)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={year}
                                sx={labelStyles}
                            />
                        ))}
                    </Stack>
                </Box>
            </Box>

            <Divider sx={{ my: 3, borderColor: 'rgba(46, 192, 203, 0.1)' }} />

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Category
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Categories..."
                        size="small"
                        fullWidth
                        value={searchFilters.categories}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, categories: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Stack spacing={0.5} maxHeight={120} overflow="auto">
                        {filterOptionsBySearch(filterOptions.categories, searchFilters.categories).map(category => (
                            <FormControlLabel
                                key={category}
                                control={
                                    <Checkbox
                                        checked={filters.categories.includes(category)}
                                        onChange={() => handleFilterChange('categories', category)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={category}
                                sx={labelStyles}
                            />
                        ))}
                    </Stack>
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Brand
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Brands..."
                        size="small"
                        fullWidth
                        value={searchFilters.brands}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, brands: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Stack spacing={0.5} maxHeight={120} overflow="auto">
                        {filterOptionsBySearch(filterOptions.brands, searchFilters.brands).map(brand => (
                            <FormControlLabel
                                key={brand}
                                control={
                                    <Checkbox
                                        checked={filters.brands.includes(brand)}
                                        onChange={() => handleFilterChange('brands', brand)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={brand}
                                sx={labelStyles}
                            />
                        ))}
                    </Stack>
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" fontWeight={500} mb={1} color="text.primary" sx={{ fontSize: '0.85rem', px: 0.5 }}>
                    Price Range
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    {/* Price Input Fields */}
                    <Stack direction="row" spacing={1} mb={2}>
                        <TextField
                            placeholder="Min"
                            size="small"
                            type="number"
                            value={filters.priceRange[0]}
                            onChange={(e) => {
                                const newMin = Math.max(0, parseInt(e.target.value) || 0);
                                setFilters(prev => ({
                                    ...prev,
                                    priceRange: [newMin, Math.max(newMin, prev.priceRange[1])]
                                }));
                            }}
                            sx={{
                                flex: 1,
                                '& .MuiOutlinedInput-root': {
                                    fontSize: '0.8rem',
                                    '& fieldset': { borderColor: 'rgba(46, 192, 203, 0.3)' },
                                    '&:hover fieldset': { borderColor: 'rgba(46, 192, 203, 0.5)' },
                                    '&.Mui-focused fieldset': { borderColor: 'primary.main' }
                                }
                            }}
                        />
                        <TextField
                            placeholder="Max"
                            size="small"
                            type="number"
                            value={filters.priceRange[1]}
                            onChange={(e) => {
                                const newMax = Math.min(10000, parseInt(e.target.value) || 1000);
                                setFilters(prev => ({
                                    ...prev,
                                    priceRange: [Math.min(prev.priceRange[0], newMax), newMax]
                                }));
                            }}
                            sx={{
                                flex: 1,
                                '& .MuiOutlinedInput-root': {
                                    fontSize: '0.8rem',
                                    '& fieldset': { borderColor: 'rgba(46, 192, 203, 0.3)' },
                                    '&:hover fieldset': { borderColor: 'rgba(46, 192, 203, 0.5)' },
                                    '&.Mui-focused fieldset': { borderColor: 'primary.main' }
                                }
                            }}
                        />
                    </Stack>

                    {/* Price Slider */}
                    <Slider
                        value={filters.priceRange}
                        onChange={handlePriceChange}
                        valueLabelDisplay="auto"
                        min={0}
                        max={10000}
                        step={50}
                        sx={{
                            color: 'primary.main',
                            '& .MuiSlider-thumb': {
                                '&:hover, &.Mui-focusVisible': {
                                    boxShadow: '0 0 0 8px rgba(46, 192, 203, 0.16)'
                                }
                            },
                            '& .MuiSlider-track': {
                                background: 'linear-gradient(90deg, #2EC0CB, #23A3AD)'
                            }
                        }}
                    />

                    {/* Price Display */}
                    <Typography
                        textAlign="center"
                        variant="caption"
                        sx={{
                            fontSize: '0.75rem',
                            color: 'text.secondary',
                            mt: 1,
                            display: 'block'
                        }}
                    >
                        ${filters.priceRange[0]} - ${filters.priceRange[1]}
                    </Typography>
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Material
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Materials..."
                        size="small"
                        fullWidth
                        value={searchFilters.materials}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, materials: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Stack spacing={0.5} maxHeight={120} overflow="auto">
                        {filterOptionsBySearch(filterOptions.materials, searchFilters.materials).map(material => (
                            <FormControlLabel
                                key={material}
                                control={
                                    <Checkbox
                                        checked={filters.materials.includes(material)}
                                        onChange={() => handleFilterChange('materials', material)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={material}
                                sx={labelStyles}
                            />
                        ))}
                    </Stack>
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Warranty
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Warranties..."
                        size="small"
                        fullWidth
                        value={searchFilters.warranties}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, warranties: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Stack spacing={0.5} maxHeight={120} overflow="auto">
                        {filterOptionsBySearch(filterOptions.warranties, searchFilters.warranties).map(warranty => (
                            <FormControlLabel
                                key={warranty}
                                control={
                                    <Checkbox
                                        checked={filters.warranties.includes(warranty)}
                                        onChange={() => handleFilterChange('warranties', warranty)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={warranty}
                                sx={labelStyles}
                            />
                        ))}
                    </Stack>
                </Box>
            </Box>

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Stock Location
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Locations..."
                        size="small"
                        fullWidth
                        value={searchFilters.stockLocations}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, stockLocations: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Stack spacing={0.5} maxHeight={120} overflow="auto">
                        {filterOptionsBySearch(filterOptions.stockLocations, searchFilters.stockLocations).map(location => (
                            <FormControlLabel
                                key={location}
                                control={
                                    <Checkbox
                                        checked={filters.stockLocations.includes(location)}
                                        onChange={() => handleFilterChange('stockLocations', location)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={location}
                                sx={labelStyles}
                            />
                        ))}
                    </Stack>
                </Box>
            </Box>

            <Divider sx={{ my: 3, borderColor: 'rgba(46, 192, 203, 0.1)' }} />

            <Box mb={2.5}>
                <Typography variant="subtitle2" mb={1} sx={sectionTitleStyles}>
                    Part Type
                </Typography>
                <Box sx={{ px: 0.5 }}>
                    <TextField
                        placeholder="Search Part Types..."
                        size="small"
                        fullWidth
                        value={searchFilters.partTypes}
                        onChange={(e) => setSearchFilters(prev => ({ ...prev, partTypes: e.target.value }))}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <SearchIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                                    </InputAdornment>
                                )
                            }
                        }}
                        sx={{ mb: 1.5, ...inputStyles }}
                    />
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, maxHeight: 120, overflow: 'auto' }}>
                        {filterOptionsBySearch(filterOptions.partTypes, searchFilters.partTypes).map(partType => (
                            <FormControlLabel
                                key={partType}
                                control={
                                    <Checkbox
                                        checked={filters.partTypes.includes(partType)}
                                        onChange={() => handleFilterChange('partTypes', partType)}
                                        sx={checkboxStyles}
                                    />
                                }
                                label={partType.charAt(0).toUpperCase() + partType.slice(1)}
                                sx={{
                                    ...labelStyles,
                                    minWidth: 'auto',
                                    mr: 1,
                                    mb: 0.5
                                }}
                            />
                        ))}
                    </Box>

                    {/* Include Superseded Option */}
                    <Box sx={{ mt: 2, pt: 1.5, borderTop: '1px solid rgba(46, 192, 203, 0.1)' }}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={filters.includeSuperseded}
                                    onChange={(e) => setFilters(prev => ({ ...prev, includeSuperseded: e.target.checked }))}
                                    sx={checkboxStyles}
                                />
                            }
                            label="Include Superseded Parts"
                            sx={labelStyles}
                        />
                    </Box>
                </Box>
            </Box>
            </Box>
            )}
            </Box>
        </Box>
    );
}

export default FiltersSidebar;