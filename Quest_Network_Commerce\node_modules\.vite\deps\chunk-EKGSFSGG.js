import {
  Fab_default,
  elementAcceptingRef_default,
  getTransitionProps,
  reflow
} from "./chunk-QU4BCQ2D.js";
import {
  getReactElementRef
} from "./chunk-ECZ5NTMM.js";
import {
  useSlot
} from "./chunk-QYTP72U4.js";
import {
  Transition_default,
  useTimeout
} from "./chunk-24G3PFVA.js";
import {
  useControlled_default,
  useForkRef_default
} from "./chunk-RYVDMRQL.js";
import {
  isMuiElement_default
} from "./chunk-VSJJRR2B.js";
import {
  capitalize_default,
  memoTheme_default
} from "./chunk-4ZPD3YJG.js";
import {
  useDefaultProps
} from "./chunk-5L2QZYAN.js";
import {
  clamp_default,
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  require_prop_types,
  styled_default,
  useTheme
} from "./chunk-DJZ4HCWF.js";
import {
  clsx_default,
  require_react_is
} from "./chunk-4MHI5WMA.js";
import {
  require_jsx_runtime
} from "./chunk-NJLIVH7H.js";
import {
  require_react
} from "./chunk-HUL2CLQT.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/esm/SpeedDial/SpeedDial.js
var React2 = __toESM(require_react(), 1);
var import_react_is = __toESM(require_react_is(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);

// node_modules/@mui/material/esm/Zoom/Zoom.js
var React = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var styles = {
  entering: {
    transform: "none"
  },
  entered: {
    transform: "none"
  }
};
var Zoom = React.forwardRef(function Zoom2(props, ref) {
  const theme = useTheme();
  const defaultTimeout = {
    enter: theme.transitions.duration.enteringScreen,
    exit: theme.transitions.duration.leavingScreen
  };
  const {
    addEndListener,
    appear = true,
    children,
    easing,
    in: inProp,
    onEnter,
    onEntered,
    onEntering,
    onExit,
    onExited,
    onExiting,
    style,
    timeout = defaultTimeout,
    // eslint-disable-next-line react/prop-types
    TransitionComponent = Transition_default,
    ...other
  } = props;
  const nodeRef = React.useRef(null);
  const handleRef = useForkRef_default(nodeRef, getReactElementRef(children), ref);
  const normalizedTransitionCallback = (callback) => (maybeIsAppearing) => {
    if (callback) {
      const node = nodeRef.current;
      if (maybeIsAppearing === void 0) {
        callback(node);
      } else {
        callback(node, maybeIsAppearing);
      }
    }
  };
  const handleEntering = normalizedTransitionCallback(onEntering);
  const handleEnter = normalizedTransitionCallback((node, isAppearing) => {
    reflow(node);
    const transitionProps = getTransitionProps({
      style,
      timeout,
      easing
    }, {
      mode: "enter"
    });
    node.style.webkitTransition = theme.transitions.create("transform", transitionProps);
    node.style.transition = theme.transitions.create("transform", transitionProps);
    if (onEnter) {
      onEnter(node, isAppearing);
    }
  });
  const handleEntered = normalizedTransitionCallback(onEntered);
  const handleExiting = normalizedTransitionCallback(onExiting);
  const handleExit = normalizedTransitionCallback((node) => {
    const transitionProps = getTransitionProps({
      style,
      timeout,
      easing
    }, {
      mode: "exit"
    });
    node.style.webkitTransition = theme.transitions.create("transform", transitionProps);
    node.style.transition = theme.transitions.create("transform", transitionProps);
    if (onExit) {
      onExit(node);
    }
  });
  const handleExited = normalizedTransitionCallback(onExited);
  const handleAddEndListener = (next) => {
    if (addEndListener) {
      addEndListener(nodeRef.current, next);
    }
  };
  return (0, import_jsx_runtime.jsx)(TransitionComponent, {
    appear,
    in: inProp,
    nodeRef,
    onEnter: handleEnter,
    onEntered: handleEntered,
    onEntering: handleEntering,
    onExit: handleExit,
    onExited: handleExited,
    onExiting: handleExiting,
    addEndListener: handleAddEndListener,
    timeout,
    ...other,
    children: (state, {
      ownerState,
      ...restChildProps
    }) => {
      return React.cloneElement(children, {
        style: {
          transform: "scale(0)",
          visibility: state === "exited" && !inProp ? "hidden" : void 0,
          ...styles[state],
          ...style,
          ...children.props.style
        },
        ref: handleRef,
        ...restChildProps
      });
    }
  });
});
true ? Zoom.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Add a custom transition end trigger. Called with the transitioning DOM
   * node and a done callback. Allows for more fine grained transition end
   * logic. Note: Timeouts are still used as a fallback if provided.
   */
  addEndListener: import_prop_types.default.func,
  /**
   * Perform the enter transition when it first mounts if `in` is also `true`.
   * Set this to `false` to disable this behavior.
   * @default true
   */
  appear: import_prop_types.default.bool,
  /**
   * A single child content element.
   */
  children: elementAcceptingRef_default.isRequired,
  /**
   * The transition timing function.
   * You may specify a single easing or a object containing enter and exit values.
   */
  easing: import_prop_types.default.oneOfType([import_prop_types.default.shape({
    enter: import_prop_types.default.string,
    exit: import_prop_types.default.string
  }), import_prop_types.default.string]),
  /**
   * If `true`, the component will transition in.
   */
  in: import_prop_types.default.bool,
  /**
   * @ignore
   */
  onEnter: import_prop_types.default.func,
  /**
   * @ignore
   */
  onEntered: import_prop_types.default.func,
  /**
   * @ignore
   */
  onEntering: import_prop_types.default.func,
  /**
   * @ignore
   */
  onExit: import_prop_types.default.func,
  /**
   * @ignore
   */
  onExited: import_prop_types.default.func,
  /**
   * @ignore
   */
  onExiting: import_prop_types.default.func,
  /**
   * @ignore
   */
  style: import_prop_types.default.object,
  /**
   * The duration for the transition, in milliseconds.
   * You may specify a single timeout for all transitions, or individually with an object.
   * @default {
   *   enter: theme.transitions.duration.enteringScreen,
   *   exit: theme.transitions.duration.leavingScreen,
   * }
   */
  timeout: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.shape({
    appear: import_prop_types.default.number,
    enter: import_prop_types.default.number,
    exit: import_prop_types.default.number
  })])
} : void 0;
var Zoom_default = Zoom;

// node_modules/@mui/material/esm/SpeedDial/speedDialClasses.js
function getSpeedDialUtilityClass(slot) {
  return generateUtilityClass("MuiSpeedDial", slot);
}
var speedDialClasses = generateUtilityClasses("MuiSpeedDial", ["root", "fab", "directionUp", "directionDown", "directionLeft", "directionRight", "actions", "actionsClosed"]);
var speedDialClasses_default = speedDialClasses;

// node_modules/@mui/material/esm/SpeedDial/SpeedDial.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    open,
    direction
  } = ownerState;
  const slots = {
    root: ["root", `direction${capitalize_default(direction)}`],
    fab: ["fab"],
    actions: ["actions", !open && "actionsClosed"]
  };
  return composeClasses(slots, getSpeedDialUtilityClass, classes);
};
function getOrientation(direction) {
  if (direction === "up" || direction === "down") {
    return "vertical";
  }
  if (direction === "right" || direction === "left") {
    return "horizontal";
  }
  return void 0;
}
var dialRadius = 32;
var spacingActions = 16;
var SpeedDialRoot = styled_default("div", {
  name: "MuiSpeedDial",
  slot: "Root",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [styles2.root, styles2[`direction${capitalize_default(ownerState.direction)}`]];
  }
})(memoTheme_default(({
  theme
}) => ({
  zIndex: (theme.vars || theme).zIndex.speedDial,
  display: "flex",
  alignItems: "center",
  pointerEvents: "none",
  variants: [{
    props: {
      direction: "up"
    },
    style: {
      flexDirection: "column-reverse",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "column-reverse",
        marginBottom: -dialRadius,
        paddingBottom: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: "down"
    },
    style: {
      flexDirection: "column",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "column",
        marginTop: -dialRadius,
        paddingTop: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: "left"
    },
    style: {
      flexDirection: "row-reverse",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "row-reverse",
        marginRight: -dialRadius,
        paddingRight: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: "right"
    },
    style: {
      flexDirection: "row",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "row",
        marginLeft: -dialRadius,
        paddingLeft: spacingActions + dialRadius
      }
    }
  }]
})));
var SpeedDialFab = styled_default(Fab_default, {
  name: "MuiSpeedDial",
  slot: "Fab"
})({
  pointerEvents: "auto"
});
var SpeedDialActions = styled_default("div", {
  name: "MuiSpeedDial",
  slot: "Actions",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [styles2.actions, !ownerState.open && styles2.actionsClosed];
  }
})({
  display: "flex",
  pointerEvents: "auto",
  variants: [{
    props: ({
      ownerState
    }) => !ownerState.open,
    style: {
      transition: "top 0s linear 0.2s",
      pointerEvents: "none"
    }
  }]
});
var SpeedDial = React2.forwardRef(function SpeedDial2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiSpeedDial"
  });
  const theme = useTheme();
  const defaultTransitionDuration = {
    enter: theme.transitions.duration.enteringScreen,
    exit: theme.transitions.duration.leavingScreen
  };
  const {
    ariaLabel,
    FabProps: {
      ref: origDialButtonRef,
      ...FabProps
    } = {},
    children: childrenProp,
    className,
    direction = "up",
    hidden = false,
    icon,
    onBlur,
    onClose,
    onFocus,
    onKeyDown,
    onMouseEnter,
    onMouseLeave,
    onOpen,
    open: openProp,
    openIcon,
    slots = {},
    slotProps = {},
    TransitionComponent: TransitionComponentProp,
    TransitionProps: TransitionPropsProp,
    transitionDuration = defaultTransitionDuration,
    ...other
  } = props;
  const [open, setOpenState] = useControlled_default({
    controlled: openProp,
    default: false,
    name: "SpeedDial",
    state: "open"
  });
  const ownerState = {
    ...props,
    open,
    direction
  };
  const classes = useUtilityClasses(ownerState);
  const eventTimer = useTimeout();
  const focusedAction = React2.useRef(0);
  const nextItemArrowKey = React2.useRef();
  const actions = React2.useRef([]);
  actions.current = [actions.current[0]];
  const handleOwnFabRef = React2.useCallback((fabFef) => {
    actions.current[0] = fabFef;
  }, []);
  const handleFabRef = useForkRef_default(origDialButtonRef, handleOwnFabRef);
  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {
    return (buttonRef) => {
      actions.current[dialActionIndex + 1] = buttonRef;
      if (origButtonRef) {
        origButtonRef(buttonRef);
      }
    };
  };
  const handleKeyDown = (event) => {
    if (onKeyDown) {
      onKeyDown(event);
    }
    const key = event.key.replace("Arrow", "").toLowerCase();
    const {
      current: nextItemArrowKeyCurrent = key
    } = nextItemArrowKey;
    if (event.key === "Escape") {
      setOpenState(false);
      actions.current[0].focus();
      if (onClose) {
        onClose(event, "escapeKeyDown");
      }
      return;
    }
    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== void 0) {
      event.preventDefault();
      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;
      const nextAction = clamp_default(focusedAction.current + actionStep, 0, actions.current.length - 1);
      actions.current[nextAction].focus();
      focusedAction.current = nextAction;
      nextItemArrowKey.current = nextItemArrowKeyCurrent;
    }
  };
  React2.useEffect(() => {
    if (!open) {
      focusedAction.current = 0;
      nextItemArrowKey.current = void 0;
    }
  }, [open]);
  const handleClose = (event) => {
    if (event.type === "mouseleave" && onMouseLeave) {
      onMouseLeave(event);
    }
    if (event.type === "blur" && onBlur) {
      onBlur(event);
    }
    eventTimer.clear();
    if (event.type === "blur") {
      eventTimer.start(0, () => {
        setOpenState(false);
        if (onClose) {
          onClose(event, "blur");
        }
      });
    } else {
      setOpenState(false);
      if (onClose) {
        onClose(event, "mouseLeave");
      }
    }
  };
  const handleClick = (event) => {
    if (FabProps.onClick) {
      FabProps.onClick(event);
    }
    eventTimer.clear();
    if (open) {
      setOpenState(false);
      if (onClose) {
        onClose(event, "toggle");
      }
    } else {
      setOpenState(true);
      if (onOpen) {
        onOpen(event, "toggle");
      }
    }
  };
  const handleOpen = (event) => {
    if (event.type === "mouseenter" && onMouseEnter) {
      onMouseEnter(event);
    }
    if (event.type === "focus" && onFocus) {
      onFocus(event);
    }
    eventTimer.clear();
    if (!open) {
      eventTimer.start(0, () => {
        setOpenState(true);
        if (onOpen) {
          const eventMap = {
            focus: "focus",
            mouseenter: "mouseEnter"
          };
          onOpen(event, eventMap[event.type]);
        }
      });
    }
  };
  const id = ariaLabel.replace(/^[^a-z]+|[^\w:.-]+/gi, "");
  const allItems = React2.Children.toArray(childrenProp).filter((child) => {
    if (true) {
      if ((0, import_react_is.isFragment)(child)) {
        console.error(["MUI: The SpeedDial component doesn't accept a Fragment as a child.", "Consider providing an array instead."].join("\n"));
      }
    }
    return React2.isValidElement(child);
  });
  const children = allItems.map((child, index) => {
    const {
      FabProps: {
        ref: origButtonRef,
        ...ChildFabProps
      } = {},
      tooltipPlacement: tooltipPlacementProp
    } = child.props;
    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === "vertical" ? "left" : "top");
    return React2.cloneElement(child, {
      FabProps: {
        ...ChildFabProps,
        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)
      },
      delay: 30 * (open ? index : allItems.length - index),
      open,
      tooltipPlacement,
      id: `${id}-action-${index}`
    });
  });
  const backwardCompatibleSlots = {
    transition: TransitionComponentProp,
    ...slots
  };
  const backwardCompatibleSlotProps = {
    transition: TransitionPropsProp,
    ...slotProps
  };
  const externalForwardedProps = {
    slots: backwardCompatibleSlots,
    slotProps: backwardCompatibleSlotProps
  };
  const [RootSlot, rootSlotProps] = useSlot("root", {
    elementType: SpeedDialRoot,
    externalForwardedProps: {
      ...externalForwardedProps,
      ...other
    },
    ownerState,
    ref,
    className: clsx_default(classes.root, className),
    additionalProps: {
      role: "presentation"
    },
    getSlotProps: (handlers) => ({
      ...handlers,
      onKeyDown: (event) => {
        var _a;
        (_a = handlers.onKeyDown) == null ? void 0 : _a.call(handlers, event);
        handleKeyDown(event);
      },
      onBlur: (event) => {
        var _a;
        (_a = handlers.onBlur) == null ? void 0 : _a.call(handlers, event);
        handleClose(event);
      },
      onFocus: (event) => {
        var _a;
        (_a = handlers.onFocus) == null ? void 0 : _a.call(handlers, event);
        handleOpen(event);
      },
      onMouseEnter: (event) => {
        var _a;
        (_a = handlers.onMouseEnter) == null ? void 0 : _a.call(handlers, event);
        handleOpen(event);
      },
      onMouseLeave: (event) => {
        var _a;
        (_a = handlers.onMouseLeave) == null ? void 0 : _a.call(handlers, event);
        handleClose(event);
      }
    })
  });
  const [TransitionSlot, transitionProps] = useSlot("transition", {
    elementType: Zoom_default,
    externalForwardedProps,
    ownerState
  });
  return (0, import_jsx_runtime2.jsxs)(RootSlot, {
    ...rootSlotProps,
    children: [(0, import_jsx_runtime2.jsx)(TransitionSlot, {
      in: !hidden,
      timeout: transitionDuration,
      unmountOnExit: true,
      ...transitionProps,
      children: (0, import_jsx_runtime2.jsx)(SpeedDialFab, {
        color: "primary",
        "aria-label": ariaLabel,
        "aria-haspopup": "true",
        "aria-expanded": open,
        "aria-controls": `${id}-actions`,
        ...FabProps,
        onClick: handleClick,
        className: clsx_default(classes.fab, FabProps.className),
        ref: handleFabRef,
        ownerState,
        children: React2.isValidElement(icon) && isMuiElement_default(icon, ["SpeedDialIcon"]) ? React2.cloneElement(icon, {
          open
        }) : icon
      })
    }), (0, import_jsx_runtime2.jsx)(SpeedDialActions, {
      id: `${id}-actions`,
      role: "menu",
      "aria-orientation": getOrientation(direction),
      className: clsx_default(classes.actions, !open && classes.actionsClosed),
      ownerState,
      children
    })]
  });
});
true ? SpeedDial.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The aria-label of the button element.
   * Also used to provide the `id` for the `SpeedDial` element and its children.
   */
  ariaLabel: import_prop_types2.default.string.isRequired,
  /**
   * SpeedDialActions to display when the SpeedDial is `open`.
   */
  children: import_prop_types2.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types2.default.object,
  /**
   * @ignore
   */
  className: import_prop_types2.default.string,
  /**
   * The direction the actions open relative to the floating action button.
   * @default 'up'
   */
  direction: import_prop_types2.default.oneOf(["down", "left", "right", "up"]),
  /**
   * Props applied to the [`Fab`](https://mui.com/material-ui/api/fab/) element.
   * @default {}
   */
  FabProps: import_prop_types2.default.object,
  /**
   * If `true`, the SpeedDial is hidden.
   * @default false
   */
  hidden: import_prop_types2.default.bool,
  /**
   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component
   * provides a default Icon with animation.
   */
  icon: import_prop_types2.default.node,
  /**
   * @ignore
   */
  onBlur: import_prop_types2.default.func,
  /**
   * Callback fired when the component requests to be closed.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"blur"`, `"mouseLeave"`, `"escapeKeyDown"`.
   */
  onClose: import_prop_types2.default.func,
  /**
   * @ignore
   */
  onFocus: import_prop_types2.default.func,
  /**
   * @ignore
   */
  onKeyDown: import_prop_types2.default.func,
  /**
   * @ignore
   */
  onMouseEnter: import_prop_types2.default.func,
  /**
   * @ignore
   */
  onMouseLeave: import_prop_types2.default.func,
  /**
   * Callback fired when the component requests to be open.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"focus"`, `"mouseEnter"`.
   */
  onOpen: import_prop_types2.default.func,
  /**
   * If `true`, the component is shown.
   */
  open: import_prop_types2.default.bool,
  /**
   * The icon to display in the SpeedDial Fab when the SpeedDial is open.
   */
  openIcon: import_prop_types2.default.node,
  /**
   * The props used for each slot inside.
   * @default {}
   */
  slotProps: import_prop_types2.default.shape({
    root: import_prop_types2.default.oneOfType([import_prop_types2.default.func, import_prop_types2.default.object]),
    transition: import_prop_types2.default.oneOfType([import_prop_types2.default.func, import_prop_types2.default.object])
  }),
  /**
   * The components used for each slot inside.
   * @default {}
   */
  slots: import_prop_types2.default.shape({
    root: import_prop_types2.default.elementType,
    transition: import_prop_types2.default.elementType
  }),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types2.default.oneOfType([import_prop_types2.default.arrayOf(import_prop_types2.default.oneOfType([import_prop_types2.default.func, import_prop_types2.default.object, import_prop_types2.default.bool])), import_prop_types2.default.func, import_prop_types2.default.object]),
  /**
   * The component used for the transition.
   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.
   * @default Zoom
   * * @deprecated Use `slots.transition` instead. This prop will be removed in a future major release. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
   */
  TransitionComponent: import_prop_types2.default.elementType,
  /**
   * The duration for the transition, in milliseconds.
   * You may specify a single timeout for all transitions, or individually with an object.
   * @default {
   *   enter: theme.transitions.duration.enteringScreen,
   *   exit: theme.transitions.duration.leavingScreen,
   * }
   */
  transitionDuration: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.shape({
    appear: import_prop_types2.default.number,
    enter: import_prop_types2.default.number,
    exit: import_prop_types2.default.number
  })]),
  /**
   * Props applied to the transition element.
   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.
   * @deprecated Use `slotProps.transition` instead. This prop will be removed in a future major release. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
   */
  TransitionProps: import_prop_types2.default.object
} : void 0;
var SpeedDial_default = SpeedDial;

export {
  Zoom_default,
  getSpeedDialUtilityClass,
  speedDialClasses_default,
  SpeedDial_default
};
//# sourceMappingURL=chunk-EKGSFSGG.js.map
