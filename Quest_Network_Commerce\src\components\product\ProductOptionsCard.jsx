import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Stack,
  Divider,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Tooltip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  CheckCircleIcon,
  WarningIcon,
  InfoIcon,
  StarIcon,
  LocalShippingIcon,
  AccessTimeIcon
} from '@mui/icons-material';

const ProductOptionsCard = React.memo(({
  product,
  selectedVariant,
  variantSelections,
  availableVariants,
  variantTypes,
  variantLoading,
  onVariantChange,
  pricing,
  quantity
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Helper function to format variant labels
  const formatVariantLabel = (key) => {
    return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  };

  // Helper function to get color value for color variants
  const getColorValue = (colorName) => {
    const colorMap = {
      'red': '#f44336',
      'blue': '#2196f3',
      'green': '#4caf50',
      'yellow': '#ffeb3b',
      'orange': '#ff9800',
      'purple': '#9c27b0',
      'pink': '#e91e63',
      'black': '#000000',
      'white': '#ffffff',
      'gray': '#9e9e9e',
      'grey': '#9e9e9e',
      'brown': '#795548',
      'silver': '#c0c0c0',
      'gold': '#ffd700'
    };
    return colorMap[colorName.toLowerCase()] || '#e0e0e0';
  };

  // Check if variant type is color-related
  const isColorVariant = (key) => {
    return key.toLowerCase().includes('color') || key.toLowerCase().includes('colour');
  };

  // Calculate variant price difference
  const getVariantPriceDifference = (option, variantType) => {
    if (!availableVariants || availableVariants.length === 0) return 0;
    
    const testSelections = { ...variantSelections, [variantType.key]: option.value };
    const matchingVariant = availableVariants.find(variant => {
      return Object.keys(testSelections).every(key => {
        let variantValue;
        if (typeof variant[key] === 'object' && variant[key] !== null) {
          if (variant[key].en) variantValue = variant[key].en;
          else if (variant[key].name) variantValue = variant[key].name;
          else if (variant[key].label) variantValue = variant[key].label;
          else if (variant[key].value) variantValue = variant[key].value;
          else variantValue = JSON.stringify(variant[key]);
        } else {
          variantValue = String(variant[key]);
        }
        return variantValue === testSelections[key];
      });
    });

    if (matchingVariant && matchingVariant.priceUSD && product.priceUSD) {
      return matchingVariant.priceUSD - product.priceUSD;
    }
    return 0;
  };

  // Calculate loyalty points for current selection
  const calculateLoyaltyPoints = () => {
    if (!pricing || !pricing.finalPrice) return 0;
    
    let basePoints = Math.round(pricing.finalPrice * 0.01);
    
    // Variant-specific multipliers
    let multiplier = 1;
    if (selectedVariant) {
      // Premium variants earn 1.5x points
      if (selectedVariant.priceUSD > product.priceUSD * 1.2) {
        multiplier = 1.5;
      }
    }
    
    // Quantity-based bonus points
    let bonusPoints = 0;
    if (quantity >= 10) {
      bonusPoints = Math.round(basePoints * 0.1); // 10% bonus for bulk orders
    }
    
    return Math.round(basePoints * multiplier) + bonusPoints;
  };

  // Render variant option based on type and count
  const renderVariantOption = (variantType) => {
    const isColor = isColorVariant(variantType.key);
    const optionCount = variantType.options.length;

    if (optionCount > 4 && !isColor) {
      // Dropdown for many options
      return (
        <FormControl fullWidth size="small" disabled={variantLoading}>
          <Select
            value={variantSelections[variantType.key] || ''}
            onChange={(e) => onVariantChange(variantType.key, e.target.value)}
            sx={{ bgcolor: 'background.paper' }}
            aria-label={`Select ${variantType.label}`}
            inputProps={{
              'aria-describedby': `${variantType.key}-help-text`
            }}
          >
            {variantType.options.map((option) => (
              <MenuItem 
                key={option.uniqueKey} 
                value={option.value}
                disabled={!option.available}
              >
                <Stack direction="row" justifyContent="space-between" width="100%" alignItems="center">
                  <span>{option.label}</span>
                  <Stack direction="row" spacing={1} alignItems="center">
                    {(() => {
                      const priceDiff = getVariantPriceDifference(option, variantType);
                      return priceDiff !== 0 && (
                        <Chip 
                          label={priceDiff > 0 ? `+$${priceDiff}` : `-$${Math.abs(priceDiff)}`}
                          size="small"
                          color={priceDiff > 0 ? "warning" : "success"}
                          variant="outlined"
                        />
                      );
                    })()}
                    {!option.available && (
                      <Chip label="Out of Stock" size="small" color="error" />
                    )}
                  </Stack>
                </Stack>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      );
    } else {
      // Card-based selector for fewer options or color variants
      return (
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {variantType.options.map((option) => {
            const isSelected = variantSelections[variantType.key] === option.value;
            const priceDiff = getVariantPriceDifference(option, variantType);
            
            return (
              <Tooltip 
                key={option.uniqueKey}
                title={!option.available ? "Out of Stock" : (priceDiff !== 0 ? `${priceDiff > 0 ? '+' : ''}$${priceDiff}` : '')}
                arrow
              >
                <Card
                  component="button"
                  sx={{
                    minWidth: isColor ? 50 : 80,
                    height: isColor ? 50 : 'auto',
                    cursor: option.available ? 'pointer' : 'not-allowed',
                    border: 2,
                    borderColor: isSelected ? 'primary.main' : 'divider',
                    bgcolor: isSelected ? 'primary.50' : option.available ? 'background.paper' : 'grey.100',
                    transition: 'all 0.2s ease',
                    opacity: option.available ? 1 : 0.5,
                    position: 'relative',
                    '&:hover': option.available ? {
                      borderColor: 'primary.main',
                      transform: 'translateY(-1px)',
                      boxShadow: 2
                    } : {},
                    '&:focus': {
                      outline: '2px solid',
                      outlineColor: 'primary.main',
                      outlineOffset: '2px'
                    }
                  }}
                  onClick={() => option.available && onVariantChange(variantType.key, option.value)}
                  onKeyDown={(e) => {
                    if ((e.key === 'Enter' || e.key === ' ') && option.available) {
                      e.preventDefault();
                      onVariantChange(variantType.key, option.value);
                    }
                  }}
                  disabled={!option.available}
                  aria-label={`${variantType.label}: ${option.label}${isSelected ? ' (selected)' : ''}${!option.available ? ' (out of stock)' : ''}`}
                  aria-pressed={isSelected}
                  role="radio"
                  tabIndex={0}
                >
                  <CardContent sx={{ 
                    p: isColor ? 0 : 1.5, 
                    textAlign: 'center', 
                    '&:last-child': { pb: isColor ? 0 : 1.5 },
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {isColor ? (
                      <Box
                        sx={{
                          width: '100%',
                          height: '100%',
                          backgroundColor: getColorValue(option.value),
                          border: option.value.toLowerCase() === 'white' ? '1px solid #e0e0e0' : 'none',
                          borderRadius: 1
                        }}
                      />
                    ) : (
                      <Stack spacing={0.5} alignItems="center">
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            fontWeight: isSelected ? 600 : 400,
                            color: option.available ? 'text.primary' : 'text.disabled',
                            fontSize: '0.75rem'
                          }}
                        >
                          {option.label}
                        </Typography>
                        {priceDiff !== 0 && (
                          <Chip 
                            label={priceDiff > 0 ? `+$${priceDiff}` : `-$${Math.abs(priceDiff)}`}
                            size="small"
                            color={priceDiff > 0 ? "warning" : "success"}
                            variant="outlined"
                            sx={{ fontSize: '0.6rem', height: 16 }}
                          />
                        )}
                      </Stack>
                    )}
                  </CardContent>
                  {!option.available && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'rgba(255, 255, 255, 0.8)',
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="caption" color="error" sx={{ fontSize: '0.6rem', fontWeight: 600 }}>
                        OUT
                      </Typography>
                    </Box>
                  )}
                </Card>
              </Tooltip>
            );
          })}
        </Stack>
      );
    }
  };

  if (!product) {
    return null;
  }

  return (
    <Card elevation={2} sx={{ borderRadius: 2 }}>
      <CardContent sx={{ p: 3 }}>
        <Stack spacing={3}>
          {/* Header */}
          <Box>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
              Product Options
            </Typography>
            {selectedVariant?.sku && (
              <Typography variant="body2" color="text.secondary">
                SKU: {selectedVariant.sku}
              </Typography>
            )}
          </Box>

          {/* Variant Selection */}
          {variantTypes.length > 0 && (
            <>
              <Divider />
              <Stack spacing={2}>
                {variantTypes.map((variantType) => (
                  <Box key={variantType.key}>
                    <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                      {variantType.label}
                    </Typography>
                    {variantLoading ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CircularProgress size={16} />
                        <Typography variant="body2" color="text.secondary">
                          Loading options...
                        </Typography>
                      </Box>
                    ) : (
                      renderVariantOption(variantType)
                    )}
                  </Box>
                ))}
              </Stack>
            </>
          )}

          {/* Selected Configuration Summary */}
          {selectedVariant && Object.keys(variantSelections).length > 0 && (
            <>
              <Divider />
              <Box>
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                  Selected Configuration
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                  {Object.entries(variantSelections).map(([key, value]) => (
                    <Chip
                      key={key}
                      label={`${formatVariantLabel(key)}: ${String(value)}`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Stack>
              </Box>
            </>
          )}

          {/* Dynamic Pricing Section */}
          {pricing && (
            <>
              <Divider />
              <Box>
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                  Pricing Details
                </Typography>

                {/* Current Price Display */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h5" color="primary.main" fontWeight={700}>
                    ${pricing.finalPrice?.toFixed(2)}
                  </Typography>
                  {selectedVariant && selectedVariant.priceUSD !== product.priceUSD && (
                    <Typography variant="body2" color="text.secondary">
                      Base: ${product.priceUSD} {selectedVariant.priceUSD > product.priceUSD ? '+' : ''}${(selectedVariant.priceUSD - product.priceUSD).toFixed(2)} variant
                    </Typography>
                  )}
                </Box>

                {/* Bulk Pricing Indicators */}
                {product.bulkPricing && product.bulkPricing.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" fontWeight={600} gutterBottom>
                      Bulk Pricing Tiers
                    </Typography>
                    <Stack spacing={0.5}>
                      {product.bulkPricing.map((tier, index) => {
                        const isActive = quantity >= tier.minQty;
                        const nextTier = product.bulkPricing[index + 1];
                        const maxQty = nextTier ? nextTier.minQty - 1 : '∞';

                        return (
                          <Box
                            key={index}
                            sx={{
                              p: 1,
                              borderRadius: 1,
                              bgcolor: isActive ? 'success.50' : 'grey.50',
                              border: 1,
                              borderColor: isActive ? 'success.200' : 'grey.200'
                            }}
                          >
                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                              <Typography variant="caption" sx={{ fontWeight: isActive ? 600 : 400 }}>
                                {tier.minQty} - {maxQty} units
                              </Typography>
                              <Chip
                                label={`${(tier.discount * 100).toFixed(0)}% OFF`}
                                size="small"
                                color={isActive ? "success" : "default"}
                                variant={isActive ? "filled" : "outlined"}
                              />
                            </Stack>
                          </Box>
                        );
                      })}
                    </Stack>
                  </Box>
                )}

                {/* Price Breakdown */}
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Stack spacing={1}>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2">Base Price (x{quantity})</Typography>
                      <Typography variant="body2">${pricing.totalPrice?.toFixed(2)}</Typography>
                    </Stack>
                    {pricing.discountAmount > 0 && (
                      <Stack direction="row" justifyContent="space-between">
                        <Typography variant="body2" color="success.main">Bulk Discount</Typography>
                        <Typography variant="body2" color="success.main">
                          -${pricing.discountAmount?.toFixed(2)}
                        </Typography>
                      </Stack>
                    )}
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2">Tax (8%)</Typography>
                      <Typography variant="body2">${pricing.tax?.toFixed(2)}</Typography>
                    </Stack>
                    <Divider />
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body1" fontWeight={600}>Total</Typography>
                      <Typography variant="body1" fontWeight={600} color="primary.main">
                        ${(pricing.finalPrice + pricing.tax)?.toFixed(2)}
                      </Typography>
                    </Stack>
                  </Stack>
                </Box>
              </Box>
            </>
          )}

          {/* Loyalty Points Section */}
          {pricing && (
            <>
              <Divider />
              <Box>
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <StarIcon color="primary" fontSize="small" />
                  Loyalty Points
                </Typography>

                <Box sx={{ p: 2, bgcolor: 'primary.50', borderRadius: 1, border: 1, borderColor: 'primary.200' }}>
                  <Stack spacing={1}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">Points Earned</Typography>
                      <Typography variant="h6" color="primary.main" fontWeight={600}>
                        {calculateLoyaltyPoints()}
                      </Typography>
                    </Stack>

                    {selectedVariant && selectedVariant.priceUSD > product.priceUSD * 1.2 && (
                      <Alert severity="info" sx={{ py: 0.5 }}>
                        <Typography variant="caption">
                          Premium variant: 1.5x points multiplier applied!
                        </Typography>
                      </Alert>
                    )}

                    {quantity >= 10 && (
                      <Alert severity="success" sx={{ py: 0.5 }}>
                        <Typography variant="caption">
                          Bulk order bonus: +10% points added!
                        </Typography>
                      </Alert>
                    )}

                    <Typography variant="caption" color="text.secondary">
                      Earn 1 point per $1 spent • Premium variants earn 1.5x points
                    </Typography>
                  </Stack>
                </Box>
              </Box>
            </>
          )}

          {/* Stock and Availability */}
          {selectedVariant && (
            <>
              <Divider />
              <Box>
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                  Availability & Delivery
                </Typography>
                <Stack spacing={2}>
                  {/* Stock Status */}
                  <Stack direction="row" spacing={1} alignItems="center">
                    {(() => {
                      const variantStock = selectedVariant.stock;
                      const isInStock = variantStock > 0 || selectedVariant.availability === 'in-stock';

                      return isInStock ? (
                        <>
                          <CheckCircleIcon color="success" fontSize="small" />
                          <Typography color="success.main" fontWeight={600}>
                            In Stock
                          </Typography>
                          {variantStock && (
                            <Typography variant="body2" color="text.secondary">
                              ({variantStock} available)
                            </Typography>
                          )}
                        </>
                      ) : (
                        <>
                          <WarningIcon color="warning" fontSize="small" />
                          <Typography color="warning.main" fontWeight={600}>
                            {selectedVariant.availability || 'Out of Stock'}
                          </Typography>
                        </>
                      );
                    })()}
                  </Stack>

                  {/* Delivery Estimates */}
                  {selectedVariant.stock > 0 && (
                    <Box sx={{ p: 1.5, bgcolor: 'info.50', borderRadius: 1, border: 1, borderColor: 'info.200' }}>
                      <Stack spacing={1}>
                        <Stack direction="row" spacing={1} alignItems="center">
                          <LocalShippingIcon color="info" fontSize="small" />
                          <Typography variant="body2" fontWeight={600}>
                            Estimated Delivery
                          </Typography>
                        </Stack>
                        <Stack spacing={0.5}>
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography variant="caption">Standard Shipping</Typography>
                            <Typography variant="caption" fontWeight={600}>3-5 business days</Typography>
                          </Stack>
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography variant="caption">Express Shipping</Typography>
                            <Typography variant="caption" fontWeight={600}>1-2 business days</Typography>
                          </Stack>
                          {quantity >= 10 && (
                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                              <Typography variant="caption">Bulk Order</Typography>
                              <Typography variant="caption" fontWeight={600}>5-7 business days</Typography>
                            </Stack>
                          )}
                        </Stack>
                      </Stack>
                    </Box>
                  )}

                  {/* Low Stock Warning */}
                  {selectedVariant.stock > 0 && selectedVariant.stock <= 5 && (
                    <Alert severity="warning" sx={{ py: 0.5 }}>
                      <Typography variant="caption">
                        Only {selectedVariant.stock} left in stock - order soon!
                      </Typography>
                    </Alert>
                  )}

                  {/* High Demand Indicator */}
                  {selectedVariant.stock > 0 && selectedVariant.stock <= 10 && quantity >= 5 && (
                    <Alert severity="info" sx={{ py: 0.5 }}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <AccessTimeIcon fontSize="small" />
                        <Typography variant="caption">
                          High demand item - consider ordering extra quantity
                        </Typography>
                      </Stack>
                    </Alert>
                  )}
                </Stack>
              </Box>
            </>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
});

export default ProductOptionsCard;
